(window.webpackJsonp=window.webpackJsonp||[]).push([["view-super-merchant-admin-components-deductSetting","view-merchant-consumption-rules-service-admin-AddChargeServiceRule","view-merchant-meal-management-components-mealFoodList-FoodDiscountDialog"],{4770:function(t,e,r){"use strict";r("75cf")},"70b9":function(t,e,r){"use strict";r.r(e);var i=r("ed08"),a=r("aa47"),n=r("d0dd"),o=r("da92");function s(t){return(s="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function c(){/*! regenerator-runtime -- Copyright (c) 2014-present, Facebook, Inc. -- license (MIT): https://github.com/facebook/regenerator/blob/main/LICENSE */c=function(){return e};var t,e={},r=Object.prototype,i=r.hasOwnProperty,a=Object.defineProperty||function(t,e,r){t[e]=r.value},n="function"==typeof Symbol?Symbol:{},o=n.iterator||"@@iterator",l=n.asyncIterator||"@@asyncIterator",u=n.toStringTag||"@@toStringTag";function f(t,e,r){return Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}),t[e]}try{f({},"")}catch(t){f=function(t,e,r){return t[e]=r}}function p(t,e,r,i){var n=e&&e.prototype instanceof y?e:y,o=Object.create(n.prototype),s=new $(i||[]);return a(o,"_invoke",{value:C(t,r,s)}),o}function d(t,e,r){try{return{type:"normal",arg:t.call(e,r)}}catch(t){return{type:"throw",arg:t}}}e.wrap=p;var m="suspendedStart",g="executing",v="completed",h={};function y(){}function _(){}function b(){}var w={};f(w,o,(function(){return this}));var S=Object.getPrototypeOf,D=S&&S(S(O([])));D&&D!==r&&i.call(D,o)&&(w=D);var x=b.prototype=y.prototype=Object.create(w);function L(t){["next","throw","return"].forEach((function(e){f(t,e,(function(t){return this._invoke(e,t)}))}))}function k(t,e){function r(a,n,o,c){var l=d(t[a],t,n);if("throw"!==l.type){var u=l.arg,f=u.value;return f&&"object"==s(f)&&i.call(f,"__await")?e.resolve(f.__await).then((function(t){r("next",t,o,c)}),(function(t){r("throw",t,o,c)})):e.resolve(f).then((function(t){u.value=t,o(u)}),(function(t){return r("throw",t,o,c)}))}c(l.arg)}var n;a(this,"_invoke",{value:function(t,i){function a(){return new e((function(e,a){r(t,i,e,a)}))}return n=n?n.then(a,a):a()}})}function C(e,r,i){var a=m;return function(n,o){if(a===g)throw Error("Generator is already running");if(a===v){if("throw"===n)throw o;return{value:t,done:!0}}for(i.method=n,i.arg=o;;){var s=i.delegate;if(s){var c=F(s,i);if(c){if(c===h)continue;return c}}if("next"===i.method)i.sent=i._sent=i.arg;else if("throw"===i.method){if(a===m)throw a=v,i.arg;i.dispatchException(i.arg)}else"return"===i.method&&i.abrupt("return",i.arg);a=g;var l=d(e,r,i);if("normal"===l.type){if(a=i.done?v:"suspendedYield",l.arg===h)continue;return{value:l.arg,done:i.done}}"throw"===l.type&&(a=v,i.method="throw",i.arg=l.arg)}}}function F(e,r){var i=r.method,a=e.iterator[i];if(a===t)return r.delegate=null,"throw"===i&&e.iterator.return&&(r.method="return",r.arg=t,F(e,r),"throw"===r.method)||"return"!==i&&(r.method="throw",r.arg=new TypeError("The iterator does not provide a '"+i+"' method")),h;var n=d(a,e.iterator,r.arg);if("throw"===n.type)return r.method="throw",r.arg=n.arg,r.delegate=null,h;var o=n.arg;return o?o.done?(r[e.resultName]=o.value,r.next=e.nextLoc,"return"!==r.method&&(r.method="next",r.arg=t),r.delegate=null,h):o:(r.method="throw",r.arg=new TypeError("iterator result is not an object"),r.delegate=null,h)}function P(t){var e={tryLoc:t[0]};1 in t&&(e.catchLoc=t[1]),2 in t&&(e.finallyLoc=t[2],e.afterLoc=t[3]),this.tryEntries.push(e)}function E(t){var e=t.completion||{};e.type="normal",delete e.arg,t.completion=e}function $(t){this.tryEntries=[{tryLoc:"root"}],t.forEach(P,this),this.reset(!0)}function O(e){if(e||""===e){var r=e[o];if(r)return r.call(e);if("function"==typeof e.next)return e;if(!isNaN(e.length)){var a=-1,n=function r(){for(;++a<e.length;)if(i.call(e,a))return r.value=e[a],r.done=!1,r;return r.value=t,r.done=!0,r};return n.next=n}}throw new TypeError(s(e)+" is not iterable")}return _.prototype=b,a(x,"constructor",{value:b,configurable:!0}),a(b,"constructor",{value:_,configurable:!0}),_.displayName=f(b,u,"GeneratorFunction"),e.isGeneratorFunction=function(t){var e="function"==typeof t&&t.constructor;return!!e&&(e===_||"GeneratorFunction"===(e.displayName||e.name))},e.mark=function(t){return Object.setPrototypeOf?Object.setPrototypeOf(t,b):(t.__proto__=b,f(t,u,"GeneratorFunction")),t.prototype=Object.create(x),t},e.awrap=function(t){return{__await:t}},L(k.prototype),f(k.prototype,l,(function(){return this})),e.AsyncIterator=k,e.async=function(t,r,i,a,n){void 0===n&&(n=Promise);var o=new k(p(t,r,i,a),n);return e.isGeneratorFunction(r)?o:o.next().then((function(t){return t.done?t.value:o.next()}))},L(x),f(x,u,"Generator"),f(x,o,(function(){return this})),f(x,"toString",(function(){return"[object Generator]"})),e.keys=function(t){var e=Object(t),r=[];for(var i in e)r.push(i);return r.reverse(),function t(){for(;r.length;){var i=r.pop();if(i in e)return t.value=i,t.done=!1,t}return t.done=!0,t}},e.values=O,$.prototype={constructor:$,reset:function(e){if(this.prev=0,this.next=0,this.sent=this._sent=t,this.done=!1,this.delegate=null,this.method="next",this.arg=t,this.tryEntries.forEach(E),!e)for(var r in this)"t"===r.charAt(0)&&i.call(this,r)&&!isNaN(+r.slice(1))&&(this[r]=t)},stop:function(){this.done=!0;var t=this.tryEntries[0].completion;if("throw"===t.type)throw t.arg;return this.rval},dispatchException:function(e){if(this.done)throw e;var r=this;function a(i,a){return s.type="throw",s.arg=e,r.next=i,a&&(r.method="next",r.arg=t),!!a}for(var n=this.tryEntries.length-1;n>=0;--n){var o=this.tryEntries[n],s=o.completion;if("root"===o.tryLoc)return a("end");if(o.tryLoc<=this.prev){var c=i.call(o,"catchLoc"),l=i.call(o,"finallyLoc");if(c&&l){if(this.prev<o.catchLoc)return a(o.catchLoc,!0);if(this.prev<o.finallyLoc)return a(o.finallyLoc)}else if(c){if(this.prev<o.catchLoc)return a(o.catchLoc,!0)}else{if(!l)throw Error("try statement without catch or finally");if(this.prev<o.finallyLoc)return a(o.finallyLoc)}}}},abrupt:function(t,e){for(var r=this.tryEntries.length-1;r>=0;--r){var a=this.tryEntries[r];if(a.tryLoc<=this.prev&&i.call(a,"finallyLoc")&&this.prev<a.finallyLoc){var n=a;break}}n&&("break"===t||"continue"===t)&&n.tryLoc<=e&&e<=n.finallyLoc&&(n=null);var o=n?n.completion:{};return o.type=t,o.arg=e,n?(this.method="next",this.next=n.finallyLoc,h):this.complete(o)},complete:function(t,e){if("throw"===t.type)throw t.arg;return"break"===t.type||"continue"===t.type?this.next=t.arg:"return"===t.type?(this.rval=this.arg=t.arg,this.method="return",this.next="end"):"normal"===t.type&&e&&(this.next=e),h},finish:function(t){for(var e=this.tryEntries.length-1;e>=0;--e){var r=this.tryEntries[e];if(r.finallyLoc===t)return this.complete(r.completion,r.afterLoc),E(r),h}},catch:function(t){for(var e=this.tryEntries.length-1;e>=0;--e){var r=this.tryEntries[e];if(r.tryLoc===t){var i=r.completion;if("throw"===i.type){var a=i.arg;E(r)}return a}}throw Error("illegal catch attempt")},delegateYield:function(e,r,i){return this.delegate={iterator:O(e),resultName:r,nextLoc:i},"next"===this.method&&(this.arg=t),h}},e}function l(t,e){return function(t){if(Array.isArray(t))return t}(t)||function(t,e){var r=null==t?null:"undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(null!=r){var i,a,n,o,s=[],c=!0,l=!1;try{if(n=(r=r.call(t)).next,0===e){if(Object(r)!==r)return;c=!1}else for(;!(c=(i=n.call(r)).done)&&(s.push(i.value),s.length!==e);c=!0);}catch(t){l=!0,a=t}finally{try{if(!c&&null!=r.return&&(o=r.return(),Object(o)!==o))return}finally{if(l)throw a}}return s}}(t,e)||function(t,e){if(t){if("string"==typeof t)return u(t,e);var r={}.toString.call(t).slice(8,-1);return"Object"===r&&t.constructor&&(r=t.constructor.name),"Map"===r||"Set"===r?Array.from(t):"Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r)?u(t,e):void 0}}(t,e)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function u(t,e){(null==e||e>t.length)&&(e=t.length);for(var r=0,i=Array(e);r<e;r++)i[r]=t[r];return i}function f(t,e,r,i,a,n,o){try{var s=t[n](o),c=s.value}catch(t){return void r(t)}s.done?e(c):Promise.resolve(c).then(i,a)}function p(t){return function(){var e=this,r=arguments;return new Promise((function(i,a){var n=t.apply(e,r);function o(t){f(n,i,a,o,s,"next",t)}function s(t){f(n,i,a,o,s,"throw",t)}o(void 0)}))}}var d={name:"SuperAddOrganization",props:{type:String,infoData:{type:Object,default:function(){return{}}},organizationData:Object,restoreHandle:Function},data:function(){return{isOpen:!1,isLoading:!1,commissionsChargeType:0,formOperate:"detail",onlineSortable:null,instoreSortable:null,pageSize:10,currentPage:1,totalCount:0,onlineWalletList:[],instoreWalletList:[],onlineSortList:[],instoreSortList:[],walletFormData:{isDuplicatePayLimit:!1,duplicatePaySecondLimit:0},walletFormRuls:{merchantId:[{required:!0,message:"商户号不能为空",trigger:"blur"}],merchantName:[{required:!0,message:"商户名称不能为空",trigger:"blur"}],payway:[{required:!0,message:"请选择充值渠道",trigger:"blur"}]},serviceSettingDialog:!1,serviceSettingDialogFormData:{service_fee_type:1,quota:"",discount:""},serviceSettingDialogRuls:{quota:[{required:!0,message:"请输入金额",trigger:"blur"},{validator:n.c,trigger:"blur"}],discount:[{required:!0,message:"请输入折扣",trigger:"blur"},{validator:n.f,trigger:"blur"}]},serviceSettingData:{}}},computed:{checkIsFormStatus:function(){var t=!1;switch(this.formOperate){case"detail":t=!1;break;case"add":t=!0}return t}},watch:{type:function(t){},organizationData:function(t){var e=this;setTimeout((function(){e.searchHandle()}),50)}},created:function(){},mounted:function(){this.initLoad()},methods:{initLoad:function(){this.setChargeSetting({organization_id:this.infoData.id}),this.getWalletPayList("online"),this.getWalletPayList("instore"),this.getSettingInfo()},refreshHandle:function(){this.currentPage=1,this.initLoad()},searchHandle:Object(i.d)((function(){this.initLoad()}),300),getWalletPayList:function(t){var e=this;return p(c().mark((function r(){var a,n,o,s,u;return c().wrap((function(r){for(;;)switch(r.prev=r.next){case 0:return e.isLoading=!0,a={organizations:[e.organizationData.id],pay_scenes:[t],company:e.organizationData.company},r.next=4,Object(i.X)(e.$apis.apiBackgroundAdminPayInfoGetOrderPayinfosPost(a));case 4:if(n=r.sent,o=l(n,2),s=o[0],u=o[1],e.isLoading=!1,!s){r.next=12;break}return e.$message.error(s.message),r.abrupt("return");case 12:0===u.code?"online"===t?(e.onlineWalletList=u.data.results.sort((function(t,e){return t.weight-e.weight})),e.onlineSortable||e.$nextTick((function(){e.initSortable(t)}))):(e.instoreWalletList=u.data.results.sort((function(t,e){return t.weight-e.weight})),e.instoreSortable||e.$nextTick((function(){e.initSortable(t)}))):e.$message.error(u.msg);case 13:case"end":return r.stop()}}),r)})))()},initSortable:function(t){var e=this;this[t+"SortList"]=this[t+"WalletList"].map((function(t){return t.id}));var r=this.$refs[t+"WalletRef"].$el.querySelector(".el-table__body-wrapper > table > tbody");this[t+"Sortable"]=a.a.create(r,{ghostClass:"sortable-active",animation:300,setData:function(t){t.setData("Text","")},onEnd:function(r){var i=e[t+"WalletList"].splice(r.oldIndex,1)[0];e[t+"WalletList"].splice(r.newIndex,0,i);var a=e[t+"SortList"].splice(r.oldIndex,1)[0];e[t+"SortList"].splice(r.newIndex,0,a)}})},determineServiceSettingDialog:function(){var t=this;this.$refs.serviceSettingForm.validate((function(e){e&&(t[t.serviceSettingData.pay_scene+"WalletList"].map((function(e,r){t.serviceSettingData.id===e.id&&(e.service_fee_type=t.serviceSettingDialogFormData.service_fee_type,e.service_fee_value=1===t.serviceSettingDialogFormData.service_fee_type?o.a.times(Number(t.serviceSettingDialogFormData.quota),100):t.serviceSettingDialogFormData.discount)})),t.serviceSettingDialog=!1)}))},getSettingInfo:function(){var t=this;return p(c().mark((function e(){var r,a,n,o;return c().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return t.isLoading=!0,e.next=3,Object(i.X)(t.$apis.apiBackgroundAdminOrganizationGetSettingsPost({id:t.organizationData.id,company:t.organizationData.company}));case 3:if(r=e.sent,a=l(r,2),n=a[0],o=a[1],t.isLoading=!1,!n){e.next=11;break}return t.$message.error(n.message),e.abrupt("return");case 11:0===o.code?(t.settingInfo=o.data,t.walletFormData.isDuplicatePayLimit=!!o.data.is_duplicate_pay_limit,t.walletFormData.duplicatePaySecondLimit=o.data.duplicate_pay_second_limit):t.$message.error(o.msg);case 12:case"end":return e.stop()}}),e)})))()},serviceSetting:function(t){this.serviceSettingData=t,this.serviceSettingDialogFormData.service_fee_type=t.service_fee_type,1===t.service_fee_type&&(this.serviceSettingDialogFormData.discount="",this.serviceSettingDialogFormData.quota=String(o.a.divide(t.service_fee_value,100))),2===t.service_fee_type&&(this.serviceSettingDialogFormData.discount=t.service_fee_value,this.serviceSettingDialogFormData.quota=""),this.serviceSettingDialog=!0},saveWalletWeightHandle:function(t){var e=this;return p(c().mark((function r(){var a,n,o,s,u,f;return c().wrap((function(r){for(;;)switch(r.prev=r.next){case 0:return a=e[t+"WalletList"].map((function(t,e){return{id:t.id,weight:e+1,service_fee_type:t.service_fee_type,service_fee_value:t.service_fee_value}})),e.isLoading=!0,n={organizations:[e.organizationData.id],pay_scene:t,payinfos:a,company:e.organizationData.company},r.next=5,Object(i.X)(e.$apis.apiBackgroundAdminPayInfoSetOrderPayinfosPost(n));case 5:if(o=r.sent,s=l(o,2),u=s[0],f=s[1],e.isLoading=!1,!u){r.next=13;break}return e.$message.error(u.message),r.abrupt("return");case 13:0===f.code?(e.$message.success(f.msg),e.getWalletPayList(t)):e.$message.error(f.msg);case 14:case"end":return r.stop()}}),r)})))()},setSeniorSettingHandle:function(){var t=this;return p(c().mark((function e(){var r,a,n,o,s;return c().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return t.isLoading=!0,r={id:t.organizationData.id,is_duplicate_pay_limit:t.walletFormData.isDuplicatePayLimit?1:0,duplicate_pay_second_limit:t.walletFormData.duplicatePaySecondLimit,company:t.organizationData.company},e.next=4,Object(i.X)(t.$apis.apiBackgroundAdminOrganizationModifySettingsPost(r));case 4:if(a=e.sent,n=l(a,2),o=n[0],s=n[1],t.isLoading=!1,!o){e.next=12;break}return t.$message.error(o.message),e.abrupt("return");case 12:0===s.code?(t.payTemplateList=s.data,t.$message.success(s.msg),t.getSettingInfo()):t.$message.error(s.msg);case 13:case"end":return e.stop()}}),e)})))()},changeCommissionsChargeType:function(){var t={type:0,organization_id:this.infoData.id,commissions_charge_type:this.commissionsChargeType};this.setChargeSetting(t)},setChargeSetting:function(t){var e=this;return p(c().mark((function r(){var a,n,o,s;return c().wrap((function(r){for(;;)switch(r.prev=r.next){case 0:return r.next=2,Object(i.X)(e.$apis.apiBackgroundPaymentPayInfoSetCommissionChargeSettingPost(t));case 2:if(a=r.sent,n=l(a,2),o=n[0],s=n[1],!o){r.next=9;break}return e.$message.error(o.message),r.abrupt("return");case 9:0===s.code?(0!==t.commissions_charge_type&&1!==t.commissions_charge_type||e.$message.success("配置成功"),e.commissionsChargeType=s.data.commissions_charge_type):e.$message.error(s.msg);case 10:case"end":return r.stop()}}),r)})))()},servicePirceFormat:function(t){return 1===t.service_fee_type?o.a.divide(t.service_fee_value,100):t.service_fee_value}}},m=(r("4770"),r("2877")),g=Object(m.a)(d,(function(){var t=this,e=t._self._c;return e("div",{directives:[{name:"loading",rawName:"v-loading",value:t.isLoading,expression:"isLoading"}],staticClass:"deductsetting-wrapper"},[e("div",{staticClass:"m-b-10"},[e("span",{staticClass:"p-r-10",staticStyle:{"font-size":"14px"}},[t._v("手续费生效方式")]),e("el-radio-group",{staticClass:"ps-radio",on:{change:t.changeCommissionsChargeType},model:{value:t.commissionsChargeType,callback:function(e){t.commissionsChargeType=e},expression:"commissionsChargeType"}},[e("el-radio",{attrs:{label:0}},[t._v("订单实收金额+手续费")])],1)],1),e("div",{staticClass:"wrapper-title"},[t._v("注：优先使用【营销活动-手续费】中的设置，当用户无分组或所在分组无规则时，生效当前【扣款设置】的手续费规则")]),t._m(0),e("div",{staticClass:"table-box"},[e("el-table",{ref:"onlineWalletRef",attrs:{width:"100%","row-key":"id",data:t.onlineWalletList,"tooltip-effect":"dark","header-row-class-name":"ps-table-header-row",stripe:""}},[e("el-table-column",{attrs:{type:"index",label:"优先级",width:"80",align:"center"}}),e("el-table-column",{attrs:{label:"扣款钱包",prop:"sub_payway_alias",align:"center"}}),e("el-table-column",{attrs:{label:"商户名称",prop:"merchant_name",align:"center"}}),e("el-table-column",{attrs:{label:"商户号",prop:"merchant_id",align:"center"}}),e("el-table-column",{attrs:{"show-overflow-tooltip":"",label:"备注",prop:"remark",align:"center"}}),e("el-table-column",{attrs:{"show-overflow-tooltip":"",label:"手续费",align:"center"},scopedSlots:t._u([{key:"default",fn:function(r){return[r.row.service_fee_value?r.row.service_fee_value?e("span",{staticClass:"ps-origin",staticStyle:{cursor:"pointer"},on:{click:function(e){return t.serviceSetting(r.row)}}},[t._v(" "+t._s(t.servicePirceFormat(r.row))+" "),e("span",[t._v(t._s(1===r.row.service_fee_type?"元":"%"))])]):t._e():e("el-button",{staticClass:"ps-text",attrs:{type:"text",size:"small",disabled:"CashPay"===r.row.payway||"PushiPay"===r.row.payway},on:{click:function(e){return t.serviceSetting(r.row)}}},[t._v(" 设置 ")])]}}])}),e("el-table-column",{attrs:{label:"操作",prop:"",align:"center"},scopedSlots:t._u([{key:"default",fn:function(t){return[e("img",{staticClass:"drop-img",attrs:{src:r("cd5c"),alt:""}})]}}])})],1)],1),e("div",{staticClass:"add-wrapper"},[e("el-button",{directives:[{name:"permission",rawName:"v-permission",value:["background.admin.pay_info.set_order_payinfos"],expression:"['background.admin.pay_info.set_order_payinfos']"}],staticClass:"ps-origin-btn",attrs:{type:"primary",size:"small"},on:{click:function(e){return t.saveWalletWeightHandle("online")}}},[t._v("保存")])],1),t._m(1),e("div",{staticClass:"table-box"},[e("el-table",{ref:"instoreWalletRef",attrs:{width:"100%","row-key":"id",data:t.instoreWalletList,"tooltip-effect":"dark","header-row-class-name":"ps-table-header-row",stripe:""}},[e("el-table-column",{attrs:{type:"index",label:"优先级",width:"80",align:"center"}}),e("el-table-column",{attrs:{label:"扣款钱包",prop:"sub_payway_alias",align:"center"}}),e("el-table-column",{attrs:{label:"商户名称",prop:"merchant_name",align:"center"}}),e("el-table-column",{attrs:{label:"商户号",prop:"merchant_id",align:"center"}}),e("el-table-column",{attrs:{"show-overflow-tooltip":"",label:"备注",prop:"remark",align:"center"}}),e("el-table-column",{attrs:{"show-overflow-tooltip":"",label:"手续费",align:"center"},scopedSlots:t._u([{key:"default",fn:function(r){return[r.row.service_fee_value?r.row.service_fee_value?e("span",{staticClass:"ps-origin",staticStyle:{cursor:"pointer"},on:{click:function(e){return t.serviceSetting(r.row)}}},[t._v(" "+t._s(t.servicePirceFormat(r.row))+" "),e("span",[t._v(t._s(1===r.row.service_fee_type?"元":"%"))])]):t._e():e("el-button",{staticClass:"ps-text",attrs:{type:"text",size:"small",disabled:"CashPay"===r.row.payway||"PushiPay"===r.row.payway},on:{click:function(e){return t.serviceSetting(r.row)}}},[t._v(" 设置 ")])]}}])}),e("el-table-column",{attrs:{label:"操作",prop:"",align:"center"},scopedSlots:t._u([{key:"default",fn:function(t){return[e("img",{staticClass:"drop-img",attrs:{src:r("cd5c"),alt:""}})]}}])})],1)],1),e("div",{staticClass:"add-wrapper"},[e("el-button",{directives:[{name:"permission",rawName:"v-permission",value:["background.admin.pay_info.set_order_payinfos"],expression:"['background.admin.pay_info.set_order_payinfos']"}],staticClass:"ps-origin-btn",attrs:{type:"primary",size:"small"},on:{click:function(e){return t.saveWalletWeightHandle("instore")}}},[t._v("保存")])],1),t._m(2),e("div",{staticClass:"form-wrapper"},[e("el-form",{ref:"walletFormRef",attrs:{model:t.walletFormData,rules:t.walletFormRuls,"label-width":"180px"}},[e("el-form-item",{attrs:{prop:"pushiPayTime",label:"重复支付限制"}},[e("el-switch",{staticClass:"wallet-margin",attrs:{"active-color":"#ff9b45"},model:{value:t.walletFormData.isDuplicatePayLimit,callback:function(e){t.$set(t.walletFormData,"isDuplicatePayLimit",e)},expression:"walletFormData.isDuplicatePayLimit"}}),e("el-input-number",{attrs:{disabled:!t.walletFormData.isDuplicatePayLimit,min:0},model:{value:t.walletFormData.duplicatePaySecondLimit,callback:function(e){t.$set(t.walletFormData,"duplicatePaySecondLimit",e)},expression:"walletFormData.duplicatePaySecondLimit"}}),e("span",{staticClass:"wallet-margin-l"},[t._v("秒内不能重复支付")])],1)],1)],1),e("div",{staticClass:"add-wrapper"},[e("el-button",{directives:[{name:"permission",rawName:"v-permission",value:["background.admin.pay_info.set_order_payinfos"],expression:"['background.admin.pay_info.set_order_payinfos']"}],staticClass:"ps-origin-btn",attrs:{type:"primary",size:"small"},on:{click:t.setSeniorSettingHandle}},[t._v("保存")])],1),e("el-dialog",{attrs:{title:"手续费设置",visible:t.serviceSettingDialog,width:"400px","custom-class":"ps-dialog"},on:{"update:visible":function(e){t.serviceSettingDialog=e}}},[e("el-form",{ref:"serviceSettingForm",attrs:{rules:t.serviceSettingDialogRuls,model:t.serviceSettingDialogFormData}},[e("div",{staticClass:"form-flex"},[e("el-form-item",{staticClass:"p-r-20"},[e("el-radio",{staticClass:"ps-radio",attrs:{label:1},model:{value:t.serviceSettingDialogFormData.service_fee_type,callback:function(e){t.$set(t.serviceSettingDialogFormData,"service_fee_type",e)},expression:"serviceSettingDialogFormData.service_fee_type"}},[t._v(" 定额 ")])],1),2!==t.serviceSettingDialogFormData.service_fee_type?e("el-form-item",{attrs:{prop:"quota"}},[e("div",{staticClass:"form-flex"},[e("el-input",{staticClass:"ps-input w-150 p-r-10",attrs:{size:"small"},model:{value:t.serviceSettingDialogFormData.quota,callback:function(e){t.$set(t.serviceSettingDialogFormData,"quota",e)},expression:"serviceSettingDialogFormData.quota"}}),e("span",[t._v("元")])],1),e("span",[t._v("实收金额=订单金额+定额")])]):t._e()],1),e("div",{staticClass:"form-flex"},[e("el-form-item",{staticClass:"p-r-20",attrs:{label:""}},[e("el-radio",{staticClass:"ps-radio",attrs:{label:2},model:{value:t.serviceSettingDialogFormData.service_fee_type,callback:function(e){t.$set(t.serviceSettingDialogFormData,"service_fee_type",e)},expression:"serviceSettingDialogFormData.service_fee_type"}},[t._v(" 百分比 ")])],1),1!==t.serviceSettingDialogFormData.service_fee_type?e("el-form-item",{attrs:{prop:"discount"}},[e("div",{staticClass:"form-flex"},[e("el-input",{staticClass:"ps-input w-150 p-r-10",attrs:{size:"small"},model:{value:t.serviceSettingDialogFormData.discount,callback:function(e){t.$set(t.serviceSettingDialogFormData,"discount",e)},expression:"serviceSettingDialogFormData.discount"}}),e("span",[t._v("%")])],1),e("span",[t._v("实收金额=订单金额+（订单金额*折扣）")])]):t._e()],1)]),e("span",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[e("el-button",{on:{click:function(e){t.serviceSettingDialog=!1}}},[t._v("取 消")]),e("el-button",{staticClass:"ps-btn",attrs:{type:"primary"},on:{click:t.determineServiceSettingDialog}},[t._v(" 确 定 ")])],1)],1)],1)}),[function(){var t=this._self._c;return t("div",{staticClass:"l-title"},[t("span",[this._v("线上扣款顺序")])])},function(){var t=this._self._c;return t("div",{staticClass:"l-title"},[t("span",[this._v("线下扣款顺序")])])},function(){var t=this._self._c;return t("div",{staticClass:"l-title"},[t("span",[this._v("扣款限制")])])}],!1,null,null,null);e.default=g.exports},"75cf":function(t,e,r){t.exports={menuText:"rgba(35,40,45,.7)",menuActiveText:"#23282d",subMenuActiveText:"#23282d",menuBg:"#fff",menuHover:"#edf1f5",subMenuBg:"#fff",subMenuHover:"rgba(0,0,0,.1)",sideBarWidth:"240px"}},d0dd:function(t,e,r){"use strict";r.d(e,"a",(function(){return i})),r.d(e,"b",(function(){return a})),r.d(e,"g",(function(){return n})),r.d(e,"c",(function(){return o})),r.d(e,"f",(function(){return s})),r.d(e,"d",(function(){return c})),r.d(e,"e",(function(){return l}));var i=function(t,e,r){if(e){/^-?(([1-9][0-9]*)|(([0]\.\d{1,2}|[1-9][0-9]*\.\d{1,2})))$/.test(e)?r():r(new Error("金额格式有误"))}else r(new Error("请输入金额"))},a=function(t,e,r){if(e){/^-?(([0-9][0-9]*)|(([0]\.\d{1,2}|[1-9][0-9]*\.\d{1,2})))$/.test(e)?r():r(new Error("金额格式有误"))}else r()},n=function(t,e,r){if(!e)return r(new Error("手机号不能为空"));/^1[3456789]\d{9}$/.test(e)?r():r(new Error("请输入正确手机号"))},o=function(t,e,r){if(!e)return r(new Error("金额有误"));/^(([0]{1,1})|([1-9][0-9]*)|(([0]\.\d{1,2}|[1-9][0-9]*\.\d{1,2})))$/.test(e)?r():r(new Error("金额格式有误"))},s=function(t,e,r){if(""===e)return r(new Error("不能为空"));/^\d+$/.test(e)?r():r(new Error("请输入正确数字"))},c=function(t,e,r){if(""!==e){/^(\+|-)?(([0-9][0-9]*)|(([0]\.\d{1,2}|[1-9][0-9]*\.\d{1,2})))$/.test(e)?r():r(new Error("金额格式有误"))}else r(new Error("请输入金额"))},l=function(t,e,r){/^[\u4E00-\u9FA5\w-]+$/.test(e)?r():r(new Error("格式不正确，不能包含特殊字符"))}}}]);