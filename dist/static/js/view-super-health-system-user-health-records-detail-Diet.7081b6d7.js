(window.webpackJsonp=window.webpackJsonp||[]).push([["view-super-health-system-user-health-records-detail-Diet","view-super-health-system-user-health-records-constants"],{"2f56":function(e,t,a){"use strict";a.r(t),a.d(t,"recentSevenDay",(function(){return r})),a.d(t,"USERHEALTHRECORDS",(function(){return o})),a.d(t,"RADAROPTION",(function(){return n})),a.d(t,"MEALTIME_SETTING",(function(){return l})),a.d(t,"BODY_DETAIL",(function(){return s}));var i=a("5a0c"),r=[i().subtract(7,"day").format("YYYY-MM-DD"),i().format("YYYY-MM-DD")],o={select_time:{type:"datetimerange",format:"yyyy-MM-dd HH:mm:ss",label:"创建时间",value:[],clearable:!1},name:{type:"input",value:"",label:"姓名",placeholder:"请输入姓名"},phone:{type:"input",value:"",label:"手机号",placeholder:"请输入手机号"},company_ids:{type:"CompanySelect",value:[],label:"组织",dataList:[],multiple:!0,checkStrictly:!0,collapseTags:!0,clearable:!0,companyOpts:{label:"name",value:"company"},companyKey:"all"},status:{type:"select",value:"",label:"档案状态",clearable:!0,dataList:[{label:"全部",value:""},{label:"使用中",value:"enable"},{label:"注销中",value:"logoff"}]}},n={title:{text:0,x:"center",y:"center",textStyle:{color:"#fd953c",fontWeight:"bolder",fontSize:28}},tooltip:{trigger:"axis"},radar:{name:{textStyle:{padding:[-10,-5]},color:"#23282d"},splitLine:{lineStyle:{type:"dashed",width:1}},splitArea:{show:!1,areaStyle:{color:"rgba(255,0,0,0)"}},indicator:[{name:"食物多样性",max:100},{name:"营养均衡",max:100},{name:"能量摄入",max:100},{name:"BMI",max:100},{name:"运动情况",max:100}]},series:[{tooltip:{trigger:"item"},type:"radar",label:{show:!1},areaStyle:{color:"#fad1ae"},data:[{name:"健康分",value:[0,0,0,0,0]}]}],color:["#fca255"]},l={tooltip:{trigger:"item",borderColor:"#FCA155",textStyle:{color:"#000",fontWeight:500},backgroundColor:"#fff",extraCssText:"box-shadow: 0 0 10px rgba(0,0,0,0.2);font-weight: 540;",formatter:function(e){var t=e.marker,a=e.percent;return t+e.name+"&nbsp;&nbsp;&nbsp;"+a+"%"}},title:{text:"0",x:"center",y:"center",top:"25%",textStyle:{color:"#fd953c",fontSize:18}},legend:{top:"62%",orient:"vertical",y:"bottom",padding:[0,0,0,0]},series:[{type:"pie",radius:["45%","60%"],avoidLabelOverlap:!1,top:"-10%",height:200,itemStyle:{borderRadius:10,borderColor:"#f1f2f5",borderWidth:3},hoverAnimation:!1,label:{show:!1,position:"center"},emphasis:{label:{show:!1,fontSize:"20",fontWeight:"bold"}},labelLine:{show:!1},data:[]}],color:["#07DED0","#FE985F","#e98397","#F97C95","#58AFFE","#F8C345"]},s={select_time:{type:"datetimerange",format:"yyyy-MM-dd HH:mm:ss",label:"创建时间",value:[],clearable:!1}}},"8aab":function(e,t,a){},"94ea":function(e,t,a){"use strict";a("8aab")},f161:function(e,t,a){"use strict";a.r(t);var i=a("2f56"),r=a("ed08"),o=a("da92"),n={props:{formInfoData:{type:Object,default:function(){return{}}}},data:function(){return{isLoading:!1,formData:{},circularChartRefList:[{key:"intake_record",data:[{value:0,name:"早餐",key:"breakfast",current:0,unit:"次"},{value:0,name:"午餐",key:"lunch",current:0,unit:"次"},{value:0,name:"晚餐",key:"dinner",current:0,unit:"次"}],color:["#07DED0","#FE985F","#e98397"]},{key:"source",data:[{value:0,name:"系统",key:"system",current:0,unit:"次"},{value:0,name:"手动",key:"user",current:0,unit:"次"}],color:["#FE985F","#f6c80e"]},{key:"intake_exceed",data:[{value:0,name:"早餐",key:"breakfast",current:0,unit:"次"},{value:0,name:"午餐",key:"lunch",current:0,unit:"次"},{value:0,name:"晚餐",key:"dinner",current:0,unit:"次"}],color:["red","#FE985F","#e98397"]},{key:"food_category",data:[{value:0,name:"谷类薯类",key:"cereals_tubers",current:0,unit:"种"},{value:0,name:"鱼禽蛋肉",key:"eggsandmeat",current:0,unit:"种"},{value:0,name:"蔬菜水果",key:"fruit_vegetable",current:0,unit:"种"},{value:0,name:"奶类豆类",key:"dairy",current:0,unit:"种"}],color:["#08d7d7","#4e95fa","#4ad96c","#727aff"]}],pieChart:{intake_record:null,source:null,intake_exceed:null,food_category:null},tabType:"food",tableData:[],dietData:{intake_exceed_total:0,intake_record_total:0,source_total:0,food_category_total:0},currentPage:1,pageSize:6,totalCount:0,foodList:[],ingredientList:[]}},watch:{formInfoData:function(e){var t=this;this.tabType="food",this.formData=e,this.dietData.intake_record_total=o.a.plus(this.formData.intake_record.breakfast,this.formData.intake_record.lunch,this.formData.intake_record.dinner),this.dietData.source_total=o.a.plus(this.formData.source.system,this.formData.source.user),this.dietData.intake_exceed_total=o.a.plus(this.formData.intake_exceed.breakfast,this.formData.intake_exceed.lunch,this.formData.intake_exceed.dinner),this.dietData.food_category_total=o.a.plus(this.formData.food_category.cereals_tubers,this.formData.food_category.dairy,this.formData.food_category.eggsandmeat,this.formData.food_category.fruit_vegetable),this.tableData=this.formData.food_list,this.foodList=this.formData.food_list,this.ingredientList=this.formData.ingredient_list,this.$nextTick((function(){t.initMealTimeDataPie()}))}},created:function(){},beforeDestroy:function(){window.removeEventListener("resize",this.resizeChartHandle)},mounted:function(){window.addEventListener("resize",this.resizeChartHandle)},methods:{initMealTimeDataPie:function(){var e=this;this.circularChartRefList.forEach((function(t){var a=t.data,r={};a.forEach((function(a){"intake_record"===t.key&&e.formData[t.key][a.key]/e.dietData.intake_record_total&&(a.value=Number((e.formData[t.key][a.key]/e.dietData.intake_record_total*100).toFixed(2))),"source"===t.key&&e.formData[t.key][a.key]/e.dietData.source_total&&(a.value=Number((e.formData[t.key][a.key]/e.dietData.source_total*100).toFixed(2))),"intake_exceed"===t.key&&e.formData[t.key][a.key]/e.dietData.intake_exceed_total&&(a.value=Number((e.formData[t.key][a.key]/e.dietData.intake_exceed_total*100).toFixed(2))),"food_category"===t.key&&e.formData[t.key][a.key]/e.dietData.food_category_total&&(a.value=Number((e.formData[t.key][a.key]/e.dietData.food_category_total*100).toFixed(2))),r[a.name]={value:a.value,current:e.formData[t.key][a.key],unit:a.unit}}));var o=i.MEALTIME_SETTING;o.legend.formatter=function(e){var t=r[e];return e+"    "+(t.value||0)+"%    "+(t.current||0)+t.unit},o.series[0].data=a,o.title.text="".concat(e.dietData[t.key+"_total"]).concat("food_category"===t.key?"种":"次"),e.pieChart[t.key]||(e.pieChart[t.key]=e.$echarts.init(e.$refs[t.key])),e.pieChart[t.key].setOption(o)}))},tableLoadmore:function(){var e=this;setTimeout((function(){e.pageSize+=10}),100)},tabClick:function(e){this.tabType=e,this.tableData=[],this.pageSize=6,this.tableData="food"===e?this.foodList:this.ingredientList},resizeChartHandle:Object(r.d)((function(){this.pieChart.intake_record&&this.pieChart.intake_record.resize(),this.pieChart.source&&this.pieChart.source.resize(),this.pieChart.intake_exceed&&this.pieChart.intake_exceed.resize(),this.pieChart.food_category&&this.pieChart.food_category.resize()}),300)}},l=(a("94ea"),a("2877")),s=Object(l.a)(n,(function(){var e=this,t=e._self._c;return t("div",{staticClass:"diet-wrapp records-wrapp-bg m-b-20"},[t("div",{staticStyle:{"font-weight":"bold"}},[e._v("饮食数据")]),t("div",{staticClass:"ps-flex flex-wrap m-t-10"},[t("div",[t("div",{staticClass:"diet-title m-r-10 m-b-10"},[e._m(0),t("div",{ref:"intake_record",staticStyle:{height:"240px"},attrs:{id:"circular_chart"}})])]),t("div",[t("div",{staticClass:"diet-title m-r-10 m-b-10"},[e._m(1),t("div",{ref:"source",staticStyle:{height:"240px"},attrs:{id:"circular_chart"}})])]),t("div",[t("div",{staticClass:"diet-title m-r-10 m-b-10"},[e._m(2),t("div",{ref:"intake_exceed",staticStyle:{height:"240px"},attrs:{id:"circular_chart"}})])]),t("div",[t("div",{staticClass:"diet-title m-r-10 m-b-10"},[e._m(3),t("div",{ref:"food_category",staticStyle:{height:"240px"},attrs:{id:"circular_chart"}})])])]),t("div",[t("div",{staticClass:"ps-flex-bw flex-align-c"},[t("div",{staticClass:"tab"},[t("div",{class:["tab-item","food"===e.tabType?"active":""],on:{click:function(t){return e.tabClick("food")}}},[e._v(" 菜品 ")]),t("div",{class:["tab-item","ingredient"===e.tabType?"active":""],on:{click:function(t){return e.tabClick("ingredient")}}},[e._v(" 食材 ")])])]),t("el-table",{directives:[{name:"loading",rawName:"v-loading",value:e.isLoading,expression:"isLoading"},{name:"tableLoadmore",rawName:"v-tableLoadmore",value:e.tableLoadmore,expression:"tableLoadmore"}],ref:"tableData",staticStyle:{width:"1200px"},attrs:{data:e.tableData.slice((e.currentPage-1)*e.pageSize,e.currentPage*e.pageSize),height:"288px",stripe:"","header-row-class-name":"ps-table-header-row"}},[t("el-table-column",{attrs:{type:"index",label:"序号",align:"center"},scopedSlots:e._u([{key:"default",fn:function(a){return[t("span",{class:"table-index-".concat(a.$index+1)},[e._v(e._s(a.$index+1))])]}}])}),t("el-table-column",{key:"name",attrs:{prop:"name",label:"food"===e.tabType?"菜品名称":"食材名称",align:"center"}}),t("el-table-column",{attrs:{prop:"count",label:"记录次数",align:"center"}}),t("el-table-column",{key:"category_name",attrs:{prop:"category_name",label:"次数占比",align:"center",width:"170"},scopedSlots:e._u([{key:"default",fn:function(e){return[t("div",[t("el-progress",{attrs:{percentage:e.row.scale,color:"#ff9246"}})],1)]}}])}),t("el-table-column",{attrs:{prop:"last_time",label:"最近一次记录",align:"center"}})],1)],1)])}),[function(){var e=this._self._c;return e("div",{staticClass:"ps-flex flex-align-c"},[e("span",{staticClass:"text p-l-10"},[this._v("饮食记录")])])},function(){var e=this._self._c;return e("div",{staticClass:"ps-flex flex-align-c"},[e("span",{staticClass:"text p-l-10"},[this._v("记录来源")])])},function(){var e=this._self._c;return e("div",{staticClass:"ps-flex flex-align-c"},[e("span",{staticClass:"text p-l-10"},[this._v("摄入超标")])])},function(){var e=this._self._c;return e("div",{staticClass:"ps-flex flex-align-c"},[e("span",{staticClass:"text p-l-10"},[this._v("食物种类")])])}],!1,null,null,null);t.default=s.exports}}]);