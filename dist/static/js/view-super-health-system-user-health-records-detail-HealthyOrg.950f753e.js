(window.webpackJsonp=window.webpackJsonp||[]).push([["view-super-health-system-user-health-records-detail-HealthyOrg"],{6361:function(t,a,e){"use strict";e.r(a);var n={props:{formInfoData:{type:Object,default:function(){return{}}}},data:function(){return{formData:{}}},watch:{formInfoData:function(t){this.formData=t}},mounted:function(){},methods:{}},s=(e("f504"),e("2877")),r=Object(s.a)(n,(function(){var t=this,a=t._self._c;return a("div",{staticClass:"healthy-org records-wrapp-bg m-b-20"},[t._m(0),a("div",{staticClass:"text"},[t._v(" "+t._s(t.formData.org_name)+" ")])])}),[function(){var t=this._self._c;return t("div",{staticClass:"p-b-10"},[t("span",{staticStyle:{"font-weight":"bold"}},[this._v("所属组织")])])}],!1,null,"812fef84",null);a.default=r.exports},b877:function(t,a,e){},f504:function(t,a,e){"use strict";e("b877")}}]);