(window.webpackJsonp=window.webpackJsonp||[]).push([["view-super-health-system-user-health-records-detail-HealthyTarget"],{"0d09":function(t,a,e){"use strict";e("4810")},4810:function(t,a,e){},"5f52":function(t,a,e){"use strict";e.r(a);var l={props:{formInfoData:{type:Object,default:function(){return{}}}},data:function(){return{formData:{}}},watch:{formInfoData:function(t){this.formData=t}},mounted:function(){},methods:{}},s=(e("0d09"),e("2877")),r=Object(s.a)(l,(function(){var t=this,a=t._self._c;return a("div",{staticClass:"detail-healthy-target"},[a("div",{staticClass:"healthy-target records-wrapp-bg m-b-20"},[a("div",{staticClass:"p-b-10 ps-flex-bw flex-align-c"},[a("span",{staticStyle:{"font-weight":"bold"}},[t._v("健康目标")]),a("span",{staticClass:"time"},[t._v("更新时间："+t._s(t.formData.healthy_target_update_time))])]),a("el-form",{ref:"form",attrs:{size:"mini",model:t.formData,"label-position":"left","label-width":"80px"}},[a("el-form-item",{attrs:{label:"最新体重：","label-width":"110px"}},[a("span",[t._v(t._s(t.formData.weight)+"kg")])]),a("el-form-item",{attrs:{label:"目标：","label-width":"110px"}},[a("span",[t._v(t._s(t.formData.healthy_target))])]),a("el-form-item",{attrs:{label:"目标体重：","label-width":"110px"}},[a("span",[t._v(t._s(t.formData.weight_target)+"kg")])]),a("el-form-item",{attrs:{label:"目标达成时间：","label-width":"110px"}},[a("span",[t._v(t._s(t.formData.target_day))])]),a("el-form-item",{attrs:{label:"坚持时间：","label-width":"110px"}},[a("span",[t._v(t._s(t.formData.adherence_days))])])],1)],1)])}),[],!1,null,null,null);a.default=r.exports}}]);