(window.webpackJsonp=window.webpackJsonp||[]).push([["view-super-health-system-threemeallist"],{dfd5:function(t,s,e){"use strict";function n(){var t=this;return new Promise((function(s,e){t.$apis.apiBackgroundAdminFoodListPost({page:1,page_size:999999}).then((function(t){sessionStorage.setItem("allFoodList",t.data.results?JSON.stringify(t.data.results):"[]"),s(t)})).catch((function(t){e(t)}))}))}e.r(s),e.d(s,"getFoodlist",(function(){return n}))}}]);