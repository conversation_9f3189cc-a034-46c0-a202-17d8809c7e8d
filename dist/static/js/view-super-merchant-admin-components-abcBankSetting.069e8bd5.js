(window.webpackJsonp=window.webpackJsonp||[]).push([["view-super-merchant-admin-components-abcBankSetting"],{"6dd4":function(t,e,r){t.exports={menuText:"rgba(35,40,45,.7)",menuActiveText:"#23282d",subMenuActiveText:"#23282d",menuBg:"#fff",menuHover:"#edf1f5",subMenuBg:"#fff",subMenuHover:"rgba(0,0,0,.1)",sideBarWidth:"240px"}},a8ac:function(t,e,r){"use strict";r("6dd4")},bbd5:function(t,e,r){"use strict";function n(t){return(n="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}r.d(e,"a",(function(){return o}));var o=function t(e){if(!e&&"object"!==n(e))throw new Error("error arguments","deepClone");var r=e.constructor===Array?[]:{};return Object.keys(e).forEach((function(o){e[o]&&"object"===n(e[o])?r[o]=t(e[o]):r[o]=e[o]})),r}},e9e9:function(t,e,r){"use strict";r.r(e);var n=r("ed08"),o=r("bbd5");function i(t){return(i="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function a(t,e){return function(t){if(Array.isArray(t))return t}(t)||function(t,e){var r=null==t?null:"undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(null!=r){var n,o,i,a,s=[],c=!0,l=!1;try{if(i=(r=r.call(t)).next,0===e){if(Object(r)!==r)return;c=!1}else for(;!(c=(n=i.call(r)).done)&&(s.push(n.value),s.length!==e);c=!0);}catch(t){l=!0,o=t}finally{try{if(!c&&null!=r.return&&(a=r.return(),Object(a)!==a))return}finally{if(l)throw o}}return s}}(t,e)||function(t,e){if(t){if("string"==typeof t)return s(t,e);var r={}.toString.call(t).slice(8,-1);return"Object"===r&&t.constructor&&(r=t.constructor.name),"Map"===r||"Set"===r?Array.from(t):"Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r)?s(t,e):void 0}}(t,e)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function s(t,e){(null==e||e>t.length)&&(e=t.length);for(var r=0,n=Array(e);r<e;r++)n[r]=t[r];return n}function c(){/*! regenerator-runtime -- Copyright (c) 2014-present, Facebook, Inc. -- license (MIT): https://github.com/facebook/regenerator/blob/main/LICENSE */c=function(){return e};var t,e={},r=Object.prototype,n=r.hasOwnProperty,o=Object.defineProperty||function(t,e,r){t[e]=r.value},a="function"==typeof Symbol?Symbol:{},s=a.iterator||"@@iterator",l=a.asyncIterator||"@@asyncIterator",u=a.toStringTag||"@@toStringTag";function f(t,e,r){return Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}),t[e]}try{f({},"")}catch(t){f=function(t,e,r){return t[e]=r}}function p(t,e,r,n){var i=e&&e.prototype instanceof b?e:b,a=Object.create(i.prototype),s=new $(n||[]);return o(a,"_invoke",{value:D(t,r,s)}),a}function h(t,e,r){try{return{type:"normal",arg:t.call(e,r)}}catch(t){return{type:"throw",arg:t}}}e.wrap=p;var d="suspendedStart",g="executing",y="completed",m={};function b(){}function v(){}function w(){}var L={};f(L,s,(function(){return this}));var x=Object.getPrototypeOf,k=x&&x(x(T([])));k&&k!==r&&n.call(k,s)&&(L=k);var _=w.prototype=b.prototype=Object.create(L);function S(t){["next","throw","return"].forEach((function(e){f(t,e,(function(t){return this._invoke(e,t)}))}))}function O(t,e){function r(o,a,s,c){var l=h(t[o],t,a);if("throw"!==l.type){var u=l.arg,f=u.value;return f&&"object"==i(f)&&n.call(f,"__await")?e.resolve(f.__await).then((function(t){r("next",t,s,c)}),(function(t){r("throw",t,s,c)})):e.resolve(f).then((function(t){u.value=t,s(u)}),(function(t){return r("throw",t,s,c)}))}c(l.arg)}var a;o(this,"_invoke",{value:function(t,n){function o(){return new e((function(e,o){r(t,n,e,o)}))}return a=a?a.then(o,o):o()}})}function D(e,r,n){var o=d;return function(i,a){if(o===g)throw Error("Generator is already running");if(o===y){if("throw"===i)throw a;return{value:t,done:!0}}for(n.method=i,n.arg=a;;){var s=n.delegate;if(s){var c=P(s,n);if(c){if(c===m)continue;return c}}if("next"===n.method)n.sent=n._sent=n.arg;else if("throw"===n.method){if(o===d)throw o=y,n.arg;n.dispatchException(n.arg)}else"return"===n.method&&n.abrupt("return",n.arg);o=g;var l=h(e,r,n);if("normal"===l.type){if(o=n.done?y:"suspendedYield",l.arg===m)continue;return{value:l.arg,done:n.done}}"throw"===l.type&&(o=y,n.method="throw",n.arg=l.arg)}}}function P(e,r){var n=r.method,o=e.iterator[n];if(o===t)return r.delegate=null,"throw"===n&&e.iterator.return&&(r.method="return",r.arg=t,P(e,r),"throw"===r.method)||"return"!==n&&(r.method="throw",r.arg=new TypeError("The iterator does not provide a '"+n+"' method")),m;var i=h(o,e.iterator,r.arg);if("throw"===i.type)return r.method="throw",r.arg=i.arg,r.delegate=null,m;var a=i.arg;return a?a.done?(r[e.resultName]=a.value,r.next=e.nextLoc,"return"!==r.method&&(r.method="next",r.arg=t),r.delegate=null,m):a:(r.method="throw",r.arg=new TypeError("iterator result is not an object"),r.delegate=null,m)}function E(t){var e={tryLoc:t[0]};1 in t&&(e.catchLoc=t[1]),2 in t&&(e.finallyLoc=t[2],e.afterLoc=t[3]),this.tryEntries.push(e)}function j(t){var e=t.completion||{};e.type="normal",delete e.arg,t.completion=e}function $(t){this.tryEntries=[{tryLoc:"root"}],t.forEach(E,this),this.reset(!0)}function T(e){if(e||""===e){var r=e[s];if(r)return r.call(e);if("function"==typeof e.next)return e;if(!isNaN(e.length)){var o=-1,a=function r(){for(;++o<e.length;)if(n.call(e,o))return r.value=e[o],r.done=!1,r;return r.value=t,r.done=!0,r};return a.next=a}}throw new TypeError(i(e)+" is not iterable")}return v.prototype=w,o(_,"constructor",{value:w,configurable:!0}),o(w,"constructor",{value:v,configurable:!0}),v.displayName=f(w,u,"GeneratorFunction"),e.isGeneratorFunction=function(t){var e="function"==typeof t&&t.constructor;return!!e&&(e===v||"GeneratorFunction"===(e.displayName||e.name))},e.mark=function(t){return Object.setPrototypeOf?Object.setPrototypeOf(t,w):(t.__proto__=w,f(t,u,"GeneratorFunction")),t.prototype=Object.create(_),t},e.awrap=function(t){return{__await:t}},S(O.prototype),f(O.prototype,l,(function(){return this})),e.AsyncIterator=O,e.async=function(t,r,n,o,i){void 0===i&&(i=Promise);var a=new O(p(t,r,n,o),i);return e.isGeneratorFunction(r)?a:a.next().then((function(t){return t.done?t.value:a.next()}))},S(_),f(_,u,"Generator"),f(_,s,(function(){return this})),f(_,"toString",(function(){return"[object Generator]"})),e.keys=function(t){var e=Object(t),r=[];for(var n in e)r.push(n);return r.reverse(),function t(){for(;r.length;){var n=r.pop();if(n in e)return t.value=n,t.done=!1,t}return t.done=!0,t}},e.values=T,$.prototype={constructor:$,reset:function(e){if(this.prev=0,this.next=0,this.sent=this._sent=t,this.done=!1,this.delegate=null,this.method="next",this.arg=t,this.tryEntries.forEach(j),!e)for(var r in this)"t"===r.charAt(0)&&n.call(this,r)&&!isNaN(+r.slice(1))&&(this[r]=t)},stop:function(){this.done=!0;var t=this.tryEntries[0].completion;if("throw"===t.type)throw t.arg;return this.rval},dispatchException:function(e){if(this.done)throw e;var r=this;function o(n,o){return s.type="throw",s.arg=e,r.next=n,o&&(r.method="next",r.arg=t),!!o}for(var i=this.tryEntries.length-1;i>=0;--i){var a=this.tryEntries[i],s=a.completion;if("root"===a.tryLoc)return o("end");if(a.tryLoc<=this.prev){var c=n.call(a,"catchLoc"),l=n.call(a,"finallyLoc");if(c&&l){if(this.prev<a.catchLoc)return o(a.catchLoc,!0);if(this.prev<a.finallyLoc)return o(a.finallyLoc)}else if(c){if(this.prev<a.catchLoc)return o(a.catchLoc,!0)}else{if(!l)throw Error("try statement without catch or finally");if(this.prev<a.finallyLoc)return o(a.finallyLoc)}}}},abrupt:function(t,e){for(var r=this.tryEntries.length-1;r>=0;--r){var o=this.tryEntries[r];if(o.tryLoc<=this.prev&&n.call(o,"finallyLoc")&&this.prev<o.finallyLoc){var i=o;break}}i&&("break"===t||"continue"===t)&&i.tryLoc<=e&&e<=i.finallyLoc&&(i=null);var a=i?i.completion:{};return a.type=t,a.arg=e,i?(this.method="next",this.next=i.finallyLoc,m):this.complete(a)},complete:function(t,e){if("throw"===t.type)throw t.arg;return"break"===t.type||"continue"===t.type?this.next=t.arg:"return"===t.type?(this.rval=this.arg=t.arg,this.method="return",this.next="end"):"normal"===t.type&&e&&(this.next=e),m},finish:function(t){for(var e=this.tryEntries.length-1;e>=0;--e){var r=this.tryEntries[e];if(r.finallyLoc===t)return this.complete(r.completion,r.afterLoc),j(r),m}},catch:function(t){for(var e=this.tryEntries.length-1;e>=0;--e){var r=this.tryEntries[e];if(r.tryLoc===t){var n=r.completion;if("throw"===n.type){var o=n.arg;j(r)}return o}}throw Error("illegal catch attempt")},delegateYield:function(e,r,n){return this.delegate={iterator:T(e),resultName:r,nextLoc:n},"next"===this.method&&(this.arg=t),m}},e}function l(t,e,r,n,o,i,a){try{var s=t[i](a),c=s.value}catch(t){return void r(t)}s.done?e(c):Promise.resolve(c).then(n,o)}function u(t){return function(){var e=this,r=arguments;return new Promise((function(n,o){var i=t.apply(e,r);function a(t){l(i,n,o,a,s,"next",t)}function s(t){l(i,n,o,a,s,"throw",t)}a(void 0)}))}}var f={name:"AbcBankSetting",props:{type:String,infoData:{type:Object,default:function(){return{}}},organizationData:Object,restoreHandle:Function},data:function(){return{isLoading:!1,isTableLoading:!1,pointList:[],formData:{},isShowPointDialog:!1,isAddPoint:!1,tableDataList:[]}},created:function(){},mounted:function(){this.initLoad()},methods:{initLoad:function(){this.getOrEditPoint("get"),this.getOrEditPoint("getByOrgs")},refreshHandle:function(){this.initLoad()},saveSettingHandle:function(){var t=this;return u(c().mark((function e(){return c().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(!t.isLoading){e.next=2;break}return e.abrupt("return");case 2:if(!t.isShowPointDialog){e.next=4;break}return e.abrupt("return",t.$message.error("请先关闭弹窗"));case 4:if(t.formData.point){e.next=6;break}return e.abrupt("return",t.$message.error("请先选择埋点项目"));case 6:t.modifySetting();case 7:case"end":return e.stop()}}),e)})))()},modifySetting:function(){var t=this;return u(c().mark((function e(){var r,o,i,s,l;return c().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return r={id:t.formData.point,company_id:t.organizationData.company},t.isLoading=!0,e.next=5,Object(n.X)(t.$apis.apiBackgroundAdminOrganizationSetComBuryInfoPost(r));case 5:if(o=e.sent,i=a(o,2),s=i[0],l=i[1],t.isLoading=!1,!s){e.next=13;break}return t.$message.error(s.message),e.abrupt("return");case 13:0===l.code?t.$message.success("修改成功"):t.$message.error(l.msg);case 14:case"end":return e.stop()}}),e)})))()},showPointDialog:function(){this.tableDataList=Object(o.a)(this.pointList),this.isShowPointDialog=!this.isShowPointDialog},addPointDialog:function(){if(this.isAddPoint)return this.$message.error("请先保存编辑数据");this.isAddPoint=!0,this.tableDataList=Object(o.a)(this.pointList);var t=Object(o.a)(this.tableDataList);t.push({name:"",type:"add"}),this.$set(this,"tableDataList",t),Reflect.has(this.$refs,"pointTable")&&this.$nextTick((function(){this.$refs.pointTable.bodyWrapper.scrollTop=this.$refs.pointTable.bodyWrapper.scrollHeight}))},getOrEditPoint:function(t,e,r){var o=this;return u(c().mark((function i(){var s,l,u,f,p,h;return c().wrap((function(i){for(;;)switch(i.prev=i.next){case 0:s={},l="",i.t0=t,i.next="get"===i.t0?6:"getByOrgs"===i.t0?8:"add"===i.t0?11:"delete"===i.t0?16:20;break;case 6:return s.is_get_list=!0,i.abrupt("break",21);case 8:return s.company_id=o.organizationData.company,s.is_get_list=!0,i.abrupt("break",21);case 11:if(e.name&&0!==e.name.length){i.next=13;break}return i.abrupt("return",o.$message.error("请先填写名称"));case 13:return s.name=e.name,l="添加成功",i.abrupt("break",21);case 16:return s.is_delete=!0,s.id=e.id,l="删除成功",i.abrupt("break",21);case 20:return i.abrupt("break",21);case 21:return o.isTableLoading=!0,o.isLoading=!0,i.next=25,Object(n.X)(o.$apis.apiBackgroundAdminOrganizationGetOrCreateBuryinfoPost(s));case 25:if(u=i.sent,f=a(u,2),p=f[0],h=f[1],o.isTableLoading=!1,o.isLoading=!1,!p){i.next=34;break}return o.$message.error(p.message),i.abrupt("return");case 34:0===h.code?o.updateTableList(t,r,l,h.data):o.$message.error(h.msg),o.isAddPoint=!1;case 36:case"end":return i.stop()}}),i)})))()},updateTableList:function(t,e,r,n){var a=Object(o.a)(this.tableDataList);"get"===t&&(this.pointList=n||[]),"getByOrgs"===t&&n&&"object"===i(n)&&Reflect.has(n,"id")&&this.$set(this.formData,"point",n.id),"add"===t&&(e>=0&&e<a.length&&(a[e].type="get"),this.$set(this,"tableDataList",a),this.$message.success(r),this.getOrEditPoint("get"),this.isAddPoint=!1),"delete"===t&&(e>=0&&e<a.length&&(a.splice(e,1),this.pointList.splice(e,1)),this.$set(this,"tableDataList",a),this.$message.success(r))},closeDialog:function(){this.isAddPoint=!1,this.isTableLoading=!1,this.isShowPointDialog=!1}}},p=(r("a8ac"),r("2877")),h=Object(p.a)(f,(function(){var t=this,e=t._self._c;return e("div",{directives:[{name:"loading",rawName:"v-loading",value:t.isLoading,expression:"isLoading"}],staticClass:"container-wrapper abcSetting"},[e("div",{staticClass:"l-title clearfix"},[e("span",{staticClass:"float-l min-title-h"},[t._v("农行配置")]),"root"==t.type?e("el-button",{staticClass:"float-r",attrs:{size:"mini"},on:{click:t.showPointDialog}},[t._v("埋点字典编辑")]):t._e()],1),"root"==t.type?e("div",{staticClass:"form-wrapper m-t-10"},[e("el-form",{ref:"abcBanksetting",attrs:{model:t.formData,size:"small","label-width":"80px"}},[e("el-form-item",{attrs:{label:"埋点：",prop:"point"}},[e("el-select",{staticClass:"ps-select w-300",attrs:{filterable:""},model:{value:t.formData.point,callback:function(e){t.$set(t.formData,"point",e)},expression:"formData.point"}},t._l(t.pointList,(function(t){return e("el-option",{key:t.id,attrs:{label:t.name,value:t.id}})})),1)],1)],1),e("div",{staticClass:"add-wrapper"},[e("el-button",{staticClass:"ps-origin-btn",attrs:{type:"primary",size:"small"},on:{click:t.saveSettingHandle}},[t._v("保存")])],1)],1):e("div",{staticClass:"empty-style m-t-20"},[e("img",{staticClass:"empty-img",attrs:{src:r("e40b")}}),e("div",{staticClass:"ps-text"},[t._v("请在第一级组织进行配置")])]),e("el-dialog",{attrs:{title:"埋点字典编辑",visible:t.isShowPointDialog,width:"600px","custom-class":"ps-dialog","close-on-click-modal":!1,"close-on-press-escape":!1},on:{"update:visible":function(e){t.isShowPointDialog=e},close:t.closeDialog}},[e("el-button",{staticClass:"float-r m-b-10",attrs:{size:"small"},on:{click:t.addPointDialog}},[t._v("添加")]),e("el-table",{directives:[{name:"loading",rawName:"v-loading",value:t.isTableLoading,expression:"isTableLoading"}],ref:"pointTable",attrs:{width:"100%",data:t.tableDataList,"tooltip-effect":"dark","max-height":"520","header-row-class-name":"ps-table-header-row","row-key":"id",stripe:""}},[e("el-table-column",{attrs:{label:"埋点项目名称",prop:"name",align:"center"},scopedSlots:t._u([{key:"default",fn:function(r){return["add"!=r.row.type?e("div",[t._v(t._s(r.row.name))]):t._e(),"add"==r.row.type?e("el-input",{staticClass:"ps-input",attrs:{placeholder:"请输入",type:"text"},model:{value:r.row.name,callback:function(e){t.$set(r.row,"name",e)},expression:"scope.row.name"}}):t._e()]}}])}),e("el-table-column",{attrs:{label:"操作",prop:"",align:"center",width:"150px",fixed:"right"},scopedSlots:t._u([{key:"default",fn:function(r){return["add"!=r.row.type?e("el-button",{staticClass:"ps-warn",attrs:{type:"text",size:"small"},on:{click:function(e){return t.getOrEditPoint("delete",r.row,r.$index)}}},[t._v(" 删除 ")]):t._e(),"add"==r.row.type?e("el-button",{staticClass:"ps-origin",attrs:{type:"text",size:"small"},on:{click:function(e){return t.getOrEditPoint("add",r.row,r.$index)}}},[t._v(" 保存 ")]):t._e()]}}])})],1),e("span",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[e("el-button",{on:{click:function(e){t.isShowPointDialog=!1}}},[t._v("关 闭")])],1)],1)],1)}),[],!1,null,null,null);e.default=h.exports}}]);