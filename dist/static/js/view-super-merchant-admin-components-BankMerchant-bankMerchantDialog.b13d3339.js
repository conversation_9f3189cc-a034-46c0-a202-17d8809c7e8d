(window.webpackJsonp=window.webpackJsonp||[]).push([["view-super-merchant-admin-components-BankMerchant-bankMerchantDialog","view-super-merchant-admin-constants-bankMerchantConstants"],{"83f7":function(e,t,a){},"941f":function(e,t,a){"use strict";a.r(t);var r=a("ed08"),i=a("ddcc");function n(e){return(n="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}function l(){/*! regenerator-runtime -- Copyright (c) 2014-present, Facebook, Inc. -- license (MIT): https://github.com/facebook/regenerator/blob/main/LICENSE */l=function(){return t};var e,t={},a=Object.prototype,r=a.hasOwnProperty,i=Object.defineProperty||function(e,t,a){e[t]=a.value},o="function"==typeof Symbol?Symbol:{},s=o.iterator||"@@iterator",c=o.asyncIterator||"@@asyncIterator",u=o.toStringTag||"@@toStringTag";function d(e,t,a){return Object.defineProperty(e,t,{value:a,enumerable:!0,configurable:!0,writable:!0}),e[t]}try{d({},"")}catch(e){d=function(e,t,a){return e[t]=a}}function p(e,t,a,r){var n=t&&t.prototype instanceof _?t:_,l=Object.create(n.prototype),o=new O(r||[]);return i(l,"_invoke",{value:w(e,a,o)}),l}function h(e,t,a){try{return{type:"normal",arg:e.call(t,a)}}catch(e){return{type:"throw",arg:e}}}t.wrap=p;var f="suspendedStart",m="executing",b="completed",y={};function _(){}function g(){}function v(){}var D={};d(D,s,(function(){return this}));var k=Object.getPrototypeOf,L=k&&k(k(S([])));L&&L!==a&&r.call(L,s)&&(D=L);var I=v.prototype=_.prototype=Object.create(D);function C(e){["next","throw","return"].forEach((function(t){d(e,t,(function(e){return this._invoke(t,e)}))}))}function E(e,t){function a(i,l,o,s){var c=h(e[i],e,l);if("throw"!==c.type){var u=c.arg,d=u.value;return d&&"object"==n(d)&&r.call(d,"__await")?t.resolve(d.__await).then((function(e){a("next",e,o,s)}),(function(e){a("throw",e,o,s)})):t.resolve(d).then((function(e){u.value=e,o(u)}),(function(e){return a("throw",e,o,s)}))}s(c.arg)}var l;i(this,"_invoke",{value:function(e,r){function i(){return new t((function(t,i){a(e,r,t,i)}))}return l=l?l.then(i,i):i()}})}function w(t,a,r){var i=f;return function(n,l){if(i===m)throw Error("Generator is already running");if(i===b){if("throw"===n)throw l;return{value:e,done:!0}}for(r.method=n,r.arg=l;;){var o=r.delegate;if(o){var s=T(o,r);if(s){if(s===y)continue;return s}}if("next"===r.method)r.sent=r._sent=r.arg;else if("throw"===r.method){if(i===f)throw i=b,r.arg;r.dispatchException(r.arg)}else"return"===r.method&&r.abrupt("return",r.arg);i=m;var c=h(t,a,r);if("normal"===c.type){if(i=r.done?b:"suspendedYield",c.arg===y)continue;return{value:c.arg,done:r.done}}"throw"===c.type&&(i=b,r.method="throw",r.arg=c.arg)}}}function T(t,a){var r=a.method,i=t.iterator[r];if(i===e)return a.delegate=null,"throw"===r&&t.iterator.return&&(a.method="return",a.arg=e,T(t,a),"throw"===a.method)||"return"!==r&&(a.method="throw",a.arg=new TypeError("The iterator does not provide a '"+r+"' method")),y;var n=h(i,t.iterator,a.arg);if("throw"===n.type)return a.method="throw",a.arg=n.arg,a.delegate=null,y;var l=n.arg;return l?l.done?(a[t.resultName]=l.value,a.next=t.nextLoc,"return"!==a.method&&(a.method="next",a.arg=e),a.delegate=null,y):l:(a.method="throw",a.arg=new TypeError("iterator result is not an object"),a.delegate=null,y)}function A(e){var t={tryLoc:e[0]};1 in e&&(t.catchLoc=e[1]),2 in e&&(t.finallyLoc=e[2],t.afterLoc=e[3]),this.tryEntries.push(t)}function N(e){var t=e.completion||{};t.type="normal",delete t.arg,e.completion=t}function O(e){this.tryEntries=[{tryLoc:"root"}],e.forEach(A,this),this.reset(!0)}function S(t){if(t||""===t){var a=t[s];if(a)return a.call(t);if("function"==typeof t.next)return t;if(!isNaN(t.length)){var i=-1,l=function a(){for(;++i<t.length;)if(r.call(t,i))return a.value=t[i],a.done=!1,a;return a.value=e,a.done=!0,a};return l.next=l}}throw new TypeError(n(t)+" is not iterable")}return g.prototype=v,i(I,"constructor",{value:v,configurable:!0}),i(v,"constructor",{value:g,configurable:!0}),g.displayName=d(v,u,"GeneratorFunction"),t.isGeneratorFunction=function(e){var t="function"==typeof e&&e.constructor;return!!t&&(t===g||"GeneratorFunction"===(t.displayName||t.name))},t.mark=function(e){return Object.setPrototypeOf?Object.setPrototypeOf(e,v):(e.__proto__=v,d(e,u,"GeneratorFunction")),e.prototype=Object.create(I),e},t.awrap=function(e){return{__await:e}},C(E.prototype),d(E.prototype,c,(function(){return this})),t.AsyncIterator=E,t.async=function(e,a,r,i,n){void 0===n&&(n=Promise);var l=new E(p(e,a,r,i),n);return t.isGeneratorFunction(a)?l:l.next().then((function(e){return e.done?e.value:l.next()}))},C(I),d(I,u,"Generator"),d(I,s,(function(){return this})),d(I,"toString",(function(){return"[object Generator]"})),t.keys=function(e){var t=Object(e),a=[];for(var r in t)a.push(r);return a.reverse(),function e(){for(;a.length;){var r=a.pop();if(r in t)return e.value=r,e.done=!1,e}return e.done=!0,e}},t.values=S,O.prototype={constructor:O,reset:function(t){if(this.prev=0,this.next=0,this.sent=this._sent=e,this.done=!1,this.delegate=null,this.method="next",this.arg=e,this.tryEntries.forEach(N),!t)for(var a in this)"t"===a.charAt(0)&&r.call(this,a)&&!isNaN(+a.slice(1))&&(this[a]=e)},stop:function(){this.done=!0;var e=this.tryEntries[0].completion;if("throw"===e.type)throw e.arg;return this.rval},dispatchException:function(t){if(this.done)throw t;var a=this;function i(r,i){return o.type="throw",o.arg=t,a.next=r,i&&(a.method="next",a.arg=e),!!i}for(var n=this.tryEntries.length-1;n>=0;--n){var l=this.tryEntries[n],o=l.completion;if("root"===l.tryLoc)return i("end");if(l.tryLoc<=this.prev){var s=r.call(l,"catchLoc"),c=r.call(l,"finallyLoc");if(s&&c){if(this.prev<l.catchLoc)return i(l.catchLoc,!0);if(this.prev<l.finallyLoc)return i(l.finallyLoc)}else if(s){if(this.prev<l.catchLoc)return i(l.catchLoc,!0)}else{if(!c)throw Error("try statement without catch or finally");if(this.prev<l.finallyLoc)return i(l.finallyLoc)}}}},abrupt:function(e,t){for(var a=this.tryEntries.length-1;a>=0;--a){var i=this.tryEntries[a];if(i.tryLoc<=this.prev&&r.call(i,"finallyLoc")&&this.prev<i.finallyLoc){var n=i;break}}n&&("break"===e||"continue"===e)&&n.tryLoc<=t&&t<=n.finallyLoc&&(n=null);var l=n?n.completion:{};return l.type=e,l.arg=t,n?(this.method="next",this.next=n.finallyLoc,y):this.complete(l)},complete:function(e,t){if("throw"===e.type)throw e.arg;return"break"===e.type||"continue"===e.type?this.next=e.arg:"return"===e.type?(this.rval=this.arg=e.arg,this.method="return",this.next="end"):"normal"===e.type&&t&&(this.next=t),y},finish:function(e){for(var t=this.tryEntries.length-1;t>=0;--t){var a=this.tryEntries[t];if(a.finallyLoc===e)return this.complete(a.completion,a.afterLoc),N(a),y}},catch:function(e){for(var t=this.tryEntries.length-1;t>=0;--t){var a=this.tryEntries[t];if(a.tryLoc===e){var r=a.completion;if("throw"===r.type){var i=r.arg;N(a)}return i}}throw Error("illegal catch attempt")},delegateYield:function(t,a,r){return this.delegate={iterator:S(t),resultName:a,nextLoc:r},"next"===this.method&&(this.arg=e),y}},t}function o(e,t){return function(e){if(Array.isArray(e))return e}(e)||function(e,t){var a=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null!=a){var r,i,n,l,o=[],s=!0,c=!1;try{if(n=(a=a.call(e)).next,0===t){if(Object(a)!==a)return;s=!1}else for(;!(s=(r=n.call(a)).done)&&(o.push(r.value),o.length!==t);s=!0);}catch(e){c=!0,i=e}finally{try{if(!s&&null!=a.return&&(l=a.return(),Object(l)!==l))return}finally{if(c)throw i}}return o}}(e,t)||function(e,t){if(e){if("string"==typeof e)return s(e,t);var a={}.toString.call(e).slice(8,-1);return"Object"===a&&e.constructor&&(a=e.constructor.name),"Map"===a||"Set"===a?Array.from(e):"Arguments"===a||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(a)?s(e,t):void 0}}(e,t)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function s(e,t){(null==t||t>e.length)&&(t=e.length);for(var a=0,r=Array(t);a<t;a++)r[a]=e[a];return r}function c(e,t,a,r,i,n,l){try{var o=e[n](l),s=o.value}catch(e){return void a(e)}o.done?t(s):Promise.resolve(s).then(r,i)}function u(e){return function(){var t=this,a=arguments;return new Promise((function(r,i){var n=e.apply(t,a);function l(e){c(n,r,i,l,o,"next",e)}function o(e){c(n,r,i,l,o,"throw",e)}l(void 0)}))}}var d={name:"BankMerchantDialog",components:{OrganizationSelect:a("cbfb").a},data:function(){return{dialogVisible:!1,type:"close",titleTxtList:["上传资料","关闭二级商户","解约二级商户","验证","历史二级商户号","上传修改"],contentTxtList:["确认关闭当前二级商户，关闭后无法重新开启","确认解约当前二级商户，解约后二级商户无法再使用","是否重新上传当前二级商户资料，上传审核通过后会重新生成新的二级商户编号"],dialogTitle:"",dialogContent:"",merchantData:{},params:{},merchantNumList:[],verifyData:{type:"message",smsCode:""},sendCodeDisabled:!1,sendAuthCode:!0,isLoading:!1,successMsg:"",fileUrl:"",fileName:"",isTableLoading:!1,actionUrl:"/api/background/file/upload",uploadParams:{prefix:"food_img_zip"},fileLists:[],headersOpts:{TOKEN:Object(r.B)()},isUploadLoading:!1,downLoadFileUrl:location.origin+"/api/temporary/template_excel/abc/新农行二级商户相关说明.xlsx",merchantTypeList:i.DIC_MERCHANT_TYPE,uploadData:{type:"idCard"},uploadDataList:Object(r.f)(i.UPLOAD_DIALOG_DATA_LIST),uploadDataCloneList:Object(r.f)(i.UPLOAD_DIALOG_DATA_LIST),protoctolData:{organizationIds:"",username:""},usernameList:[],isLoadingUser:!1,role:"super",agreementInfoId:""}},watch:{dialogVisible:function(e){e||(this.params={},this.fileName="",this.fileUrl="",this.verifyData={type:"message",smsCode:"",price:""},this.uploadData={type:"idCard",subMerClass:""},this.uploadDataList=Object(r.f)(i.UPLOAD_DIALOG_DATA_LIST),this.uploadDataCloneList=Object(r.f)(i.UPLOAD_DIALOG_DATA_LIST),this.protoctolData={organizationIds:"",username:""})}},methods:{isShowDialog:function(e){this.dialogVisible=e},submitDialogHandle:function(){switch(this.params.sub_mch_id=this.merchantData.sub_mch_id,this.type){case"close":case"break":this.successMsg="close"===this.type?"关闭成功":"解约成功",this.params.update_flag="close"===this.type?"5":"4";break;case"upload":if(!this.uploadData.subMerClass)return void this.$message.error("请选择二级商户类型");if("idCard"===this.uploadData.type&&!this.params.id_card_face_url)return void this.$message.error("请先上传法人身份证人像面照片");if("idCard"===this.uploadData.type&&!this.params.id_card_national_emblem_url)return void this.$message.error("请先上传法人身份证国徽面照片");if("other"===this.uploadData.type&&!this.params.passport_url)return void this.$message.error("请先上传法人护照、通行证照片");if("1"===this.uploadData.subMerClass&&!this.params.auxiliary_proof_url)return void this.$message.error("请先上传辅助证明材料");if(("2"===this.uploadData.subMerClass||"3"===this.uploadData.subMerClass)&&!this.params.license_url)return void this.$message.error("请先上传营业执照照片");if("4"===this.uploadData.subMerClass&&!this.params.certificate_url)return void this.$message.error("请先上传政府机关/事业单位/社会组织登记证书照片");this.successMsg="上传成功";break;case"verify":if(!this.verifyData.type||0===this.verifyData.length)return void this.$message.error("请选择验证方式");if("message"===this.verifyData.type&&(!this.verifyData.smsCode||0===this.verifyData.smsCode.length))return void this.$message.error("请填写短信验证码");if("payment"===this.verifyData.type&&(!this.verifyData.price||0===this.verifyData.price.length))return void this.$message.error("请填写打款金额");this.successMsg="验证成功",this.params.account=this.merchantData.account;var e="message"===this.verifyData.type?"verification_code":"random_amount",t="message"===this.verifyData.type?this.verifyData.smsCode:this.verifyData.price;this.$set(this.params,e,t);break;case"add":this.params=Object(r.f)(this.merchantData),delete this.params.sub_mch_id,delete this.params.isDisabledEdit,delete this.params.isShowEdit;break;case"modify":this.params=Object(r.f)(this.merchantData),this.params.ori_sub_mer_no=this.merchantData.sub_mer_id,delete this.params.isDisabledEdit,delete this.params.isShowEdit;break;case"protocol":return this.protoctolData.organizationIds?this.protoctolData.username?this.modifyBankProcotol(this.merchantData.id):this.$message.error("请选择账号"):this.$message.error("请选择组织")}"history"===this.type?this.dialogVisible=!1:this.modifyMerchant()},canceDialogHandle:function(){this.dialogVisible=!1},setDialogData:function(e,t){switch(this.type=e,this.merchantData=Object(r.f)(t),e){case"close":this.dialogTitle=this.titleTxtList[1],this.dialogContent=this.contentTxtList[0];break;case"break":this.dialogTitle=this.titleTxtList[2],this.dialogContent=this.contentTxtList[1];break;case"upload":this.dialogTitle=this.titleTxtList[0],this.dialogContent="",this.setUploadDataList();break;case"verify":this.dialogTitle=this.titleTxtList[3],this.dialogContent="";break;case"history":this.dialogTitle=this.titleTxtList[4],this.dialogContent="",this.getHistoryMerchartList(t);break;case"modify":this.dialogTitle=this.titleTxtList[5],this.dialogContent=this.contentTxtList[2];break;case"protocol":if(t&&Reflect.has(t,"get_agreement_info")){var a=t.get_agreement_info.account_id;this.agreementInfoId=t.get_agreement_info.agreement_info_id;var i=t.get_agreement_info.organization_id;i&&this.getUsernameList(i);var n={organizationIds:i,username:a}}this.protoctolData=n}},uploadMerchantData:function(){},downLoadDescription:function(){var e=document.createElement("a");e.href=this.downLoadFileUrl,e.click()},getPhoneCode:function(){var e=this;return u(l().mark((function t(){var a,i,n,s;return l().wrap((function(t){for(;;)switch(t.prev=t.next){case 0:return t.next=2,Object(r.X)(e.$apis.apiBackgroundSubMerchantInfoSendMobileMessageForSubMerPost({sub_mch_id:e.merchantData.sub_mch_id,account:e.merchantData.account}));case 2:if(a=t.sent,i=o(a,2),n=i[0],s=i[1],!n){t.next=9;break}return e.$message.error(n.messsage),t.abrupt("return");case 9:0===s.code?(e.sendAuthCode=!1,e.$message.success("发送成功")):e.$message.error(s.msg);case 10:case"end":return t.stop()}}),t)})))()},resetHandle:function(e){this.sendAuthCode=!0,this.sendCodeDisabled=!1},modifyMerchant:function(){var e=this;return u(l().mark((function t(){var a,i,n,s,c,u,d,p,h,f,m,b,y;return l().wrap((function(t){for(;;)switch(t.prev=t.next){case 0:i=(a=[])[0],n=a[1],e.isLoading=!0,t.t0=e.type,t.next="close"===t.t0||"break"===t.t0?6:"add"===t.t0?13:"modify"===t.t0?20:"verify"===t.t0?27:"upload"===t.t0?34:41;break;case 6:return t.next=8,Object(r.X)(e.$apis.apiBackgroundSubMerchantInfoUpdateSubMerchantStatusPost(e.params));case 8:return s=t.sent,c=o(s,2),i=c[0],n=c[1],t.abrupt("break",42);case 13:return t.next=15,Object(r.X)(e.$apis.apiBackgroundSubMerchantInfoAddPost(e.params));case 15:return u=t.sent,d=o(u,2),i=d[0],n=d[1],t.abrupt("break",42);case 20:return t.next=22,Object(r.X)(e.$apis.apiBackgroundSubMerchantInfoModifyPost(e.params));case 22:return p=t.sent,h=o(p,2),i=h[0],n=h[1],t.abrupt("break",42);case 27:return t.next=29,Object(r.X)(e.$apis.apiBackgroundSubMerchantInfoVerifyMessageCodeAndRandomAmountPost(e.params));case 29:return f=t.sent,m=o(f,2),i=m[0],n=m[1],t.abrupt("break",42);case 34:return t.next=36,Object(r.X)(e.$apis.apiBackgroundSubMerchantInfoSubMerchantSubmitFileNewPost(e.params));case 36:return b=t.sent,y=o(b,2),i=y[0],n=y[1],t.abrupt("break",42);case 41:return t.abrupt("break",42);case 42:if(e.isLoading=!1,!i){t.next=46;break}return e.$message.error(i.messsage),t.abrupt("return");case 46:0===n.code?(e.dialogVisible=!1,e.$message.success(e.successMsg)):e.$message.error(n.msg||"失败");case 47:case"end":return t.stop()}}),t)})))()},getHistoryMerchartList:function(e){var t=this;return u(l().mark((function a(){return l().wrap((function(a){for(;;)switch(a.prev=a.next){case 0:t.merchantNumList=Array.isArray(e)&&e.length>0?Object(r.f)(e):[];case 2:case"end":return a.stop()}}),a)})))()},beforeFileUpload:function(e){return/application\/\S*zip\S*/.test(e.type)?e.size/1024/1024<1.5?void(this.isUploadLoading=!0):(this.$message.error("上传文件大小不能超过 1.5MB!"),!1):(this.$message.error("请上传后缀名为.zip的压缩包文件"),!1)},handleFileSuccess:function(e,t,a,r,i){if(this.isUploadLoading=!1,e&&0===e.code){this.$message.success("上传成功");var n=e.data||{};this.fileUrl=n.public_url||"",this.fileName=t.name||"",this.uploadDataList[r].fileName=t.name||"",this.uploadDataList[r].fileUrl=n.public_url||"",this.uploadDataCloneList.forEach((function(e){e.fileKey===i&&(e.fileName=t.name||"",e.fileUrl=n.public_url||"")}));var l=this.uploadDataList[r].fileKey;this.$set(this.params,l,n.public_url)}else this.$message.error(e.msg||"上传失败");this.clearFile(-1,i)},handleFileError:function(e,t){this.isUploadLoading=!1,this.clearFile(-1,t)},changeSelect:function(e){this.setUploadDataList()},changeSelectIdCard:function(e){this.setUploadDataList()},setUploadDataList:function(){var e=this,t=Object(r.f)(this.uploadDataCloneList),a=[];t.forEach((function(t,r){"idCard"===e.uploadData.type&&"法人护照、通行证照片"===t.name&&(e.clearFile(r,t.fileKey),a.push(r)),"other"!==e.uploadData.type||"法人身份证人像面照片"!==t.name&&"法人身份证国徽面照片"!==t.name||(e.clearFile(r,t.fileKey),a.push(r)),"1"!==e.uploadData.subMerClass||"个体工商户/企业营业执照照片"!==t.name&&"政府机关/事业单位/社会组织登记证书照片"!==t.name||(e.clearFile(r,t.fileKey),a.push(r)),"2"!==e.uploadData.subMerClass&&"3"!==e.uploadData.subMerClass||"辅助证明材料"!==t.name&&"政府机关/事业单位/社会组织登记证书照片"!==t.name||(e.clearFile(r,t.fileKey),a.push(r)),"4"!==e.uploadData.subMerClass||"个体工商户/企业营业执照照片"!==t.name&&"辅助证明材料"!==t.name||(e.clearFile(r,t.fileKey),a.push(r))})),t=t.filter((function(e,t){return!a.includes(t)})),this.$set(this,"uploadDataList",t)},clearFile:function(e,t){e>0&&e<this.uploadDataCloneList.length&&(this.uploadDataCloneList[e].fileUrl="",this.uploadDataCloneList[e].fileName=""),Reflect.has(this.$refs,t)&&this.$refs[t].length>0&&this.$refs[t][0].clearFiles()},modifyBankProcotol:function(e){var t=this;return u(l().mark((function a(){var i,n,s,c,u;return l().wrap((function(a){for(;;)switch(a.prev=a.next){case 0:return i={organization_id:t.protoctolData.organizationIds,account_id:t.protoctolData.username,id:e},t.agreementInfoId&&(i.sub_mch_agreement_id=t.agreementInfoId),t.isLoading=!0,a.next=5,Object(r.X)(t.$apis.apiBackgroundSubMerchantInfoSubMerchantSignPost(i));case 5:if(n=a.sent,s=o(n,2),c=s[0],u=s[1],t.isLoading=!1,!c){a.next=12;break}return a.abrupt("return",t.$message.error("设置失败，"+c.message));case 12:u&&0===u.code?(t.$message.success("设置成功"),t.dialogVisible=!1,t.$emit("updateProcotol",e)):t.$message.error("设置失败，"+u.msg);case 13:case"end":return a.stop()}}),a)})))()},changeOrganization:function(e){var t=this.protoctolData.organizationIds;this.protoctolData.username="",t&&this.getUsernameList(t)},getUsernameList:function(e){var t=this;return u(l().mark((function a(){var i,n,s,c,u,d;return l().wrap((function(a){for(;;)switch(a.prev=a.next){case 0:return i={organization_ids:[e],status:1,page:1,page_size:9999},t.isLoadingUser=!0,a.next=5,Object(r.X)(t.$apis.apiBackgroundAdminAccountOrganizationAccountListPost(i));case 5:if(n=a.sent,s=o(n,2),c=s[0],u=s[1],t.isLoadingUser=!1,!c){a.next=14;break}return t.usernameList=[],a.abrupt("return");case 14:u&&0===u.code?(d=u.data||{})&&Reflect.has(d,"results")&&(t.usernameList=d.results||[]):t.usernameList=[];case 15:case"end":return a.stop()}}),a)})))()}}},p=(a("fc23"),a("2877")),h=Object(p.a)(d,(function(){var e=this,t=e._self._c;return t("div",[t("el-dialog",{attrs:{title:e.dialogTitle,visible:e.dialogVisible,width:"500px","custom-class":"ps-dialog","show-close":"","close-on-click-modal":!1},on:{"update:visible":function(t){e.dialogVisible=t}}},["close"==e.type||"break"==e.type||"modify"==e.type?t("div",{staticClass:"t-a-c"},[e._v(" "+e._s(e.dialogContent)+" ")]):"history"==e.type?t("div",{staticClass:"flex-c-center"},[t("el-table",{directives:[{name:"loading",rawName:"v-loading",value:e.isTableLoading,expression:"isTableLoading"}],staticStyle:{width:"300px"},attrs:{data:e.merchantNumList,border:"","max-height":"250","header-row-class-name":"ps-table-header-row","cell-style":{textAlign:"center"},"header-cell-style":{textAlign:"center"}}},[t("el-table-column",{attrs:{label:"二级商户号"},scopedSlots:e._u([{key:"default",fn:function(t){return[e._v(" "+e._s(t.row)+" ")]}}])})],1)],1):"upload"==e.type?t("div",{directives:[{name:"loading",rawName:"v-loading",value:e.isUploadLoading,expression:"isUploadLoading"}]},[t("div",{staticClass:"m-l-20 upload-style"},[t("el-form",[t("el-form-item",{attrs:{label:"二级商户类型："}},[t("el-select",{staticClass:"ps-select w-180",attrs:{placeholder:"请选择","popper-class":"ps-popper-select"},on:{change:function(t){return e.changeSelect(t,"subMerClass")}},model:{value:e.uploadData.subMerClass,callback:function(t){e.$set(e.uploadData,"subMerClass",t)},expression:"uploadData.subMerClass"}},e._l(e.merchantTypeList,(function(e){return t("el-option",{key:e.value,attrs:{label:e.name,value:e.value,disabled:e.disabled}})})),1)],1),t("el-form-item",{attrs:{label:"法人证件类型：",prop:"type"}},[t("el-radio-group",{on:{change:e.changeSelectIdCard},model:{value:e.uploadData.type,callback:function(t){e.$set(e.uploadData,"type",t)},expression:"uploadData.type"}},[t("el-radio",{attrs:{label:"idCard"}},[e._v("身份证")]),t("el-radio",{attrs:{label:"other"}},[e._v("其他证件")])],1)],1),e._l(e.uploadDataList,(function(a,r){return t("div",{key:r},[t("el-form-item",{attrs:{label:a.name,required:a.required}},[t("el-upload",{ref:a.fileKey,refInFor:!0,attrs:{limit:1,"before-upload":e.beforeFileUpload,"on-success":function(t,i,n){return e.handleFileSuccess(t,i,n,r,a.fileKey)},"on-error":function(t){return e.handleFileError(t,a.fileKey)},"show-file-list":!1,action:e.actionUrl,data:e.uploadParams,"auto-upload":!0,headers:e.headersOpts}},[t("span",{staticClass:"link-type"},[e._v(" 上传 ")])]),a.fileName.length>0?t("div",{staticClass:"text-gray-12"},[e._v(e._s(a.fileName))]):e._e()],1)],1)}))],2)],1),t("div",{staticClass:"text-gray-12 m-l-20"},[e._v("上传 ZIP 文件, 最大 1.5M")]),t("div",{staticClass:"m-l-20 m-t-10 text-gray-12"},[e._v("法定代表人授权函、定位证明材料、固定经营场所证明材料、合法合规用途证明材料视情况上传，具体可参考"),t("span",{staticClass:"ps-text pointer",on:{click:e.downLoadDescription}},[e._v("《二级商户说明》")])])]):"verify"==e.type?t("div",{staticClass:"flex-c-center"},[t("el-form",{ref:"form",attrs:{"label-width":"100px",model:e.verifyData}},[t("el-form-item",{attrs:{label:"验证方式：",prop:"type"}},[t("el-radio-group",{model:{value:e.verifyData.type,callback:function(t){e.$set(e.verifyData,"type",t)},expression:"verifyData.type"}},[t("el-radio",{attrs:{label:"message"}},[e._v("短信")]),t("el-radio",{attrs:{label:"payment"}},[e._v("打款")])],1)],1),"message"==e.verifyData.type?t("el-form-item",{attrs:{label:"短信验证码：",prop:"smsCode"}},[t("verification-code",{attrs:{sendAuthCode:e.sendAuthCode,disabled:e.sendCodeDisabled,"reset-handle":e.resetHandle},on:{click:e.getPhoneCode},model:{value:e.verifyData.smsCode,callback:function(t){e.$set(e.verifyData,"smsCode",t)},expression:"verifyData.smsCode"}})],1):e._e(),"payment"==e.verifyData.type?t("el-form-item",{attrs:{label:"打款金额：",prop:"price"}},[t("el-input",{attrs:{placeholder:"请输入打款金额",type:"number"},model:{value:e.verifyData.price,callback:function(t){e.$set(e.verifyData,"price",t)},expression:"verifyData.price"}})],1):e._e()],1)],1):"protocol"==e.type?t("div",{staticClass:"flex-c-center"},[t("el-form",{ref:"formProcotol",attrs:{"label-width":"100px",model:e.protoctolData}},[t("el-form-item",{attrs:{label:"发送组织：",prop:"type"}},[t("organization-select",{staticClass:"search-item-w ps-input w-180",attrs:{placeholder:"请选择组织",isLazy:!1,multiple:!1,"check-strictly":!0,"append-to-body":!0,"is-expanded-select":!0,role:e.role},on:{change:e.changeOrganization},model:{value:e.protoctolData.organizationIds,callback:function(t){e.$set(e.protoctolData,"organizationIds",t)},expression:"protoctolData.organizationIds"}})],1),t("el-form-item",{attrs:{label:"接受账号：",prop:"username"}},[t("el-select",{directives:[{name:"loading",rawName:"v-loading",value:e.isLoadingUser,expression:"isLoadingUser"}],staticClass:"ps-select w-180",attrs:{placeholder:"请选择接受账号","popper-class":"ps-popper-select",disabled:!e.protoctolData.organizationIds,filterable:""},model:{value:e.protoctolData.username,callback:function(t){e.$set(e.protoctolData,"username",t)},expression:"protoctolData.username"}},e._l(e.usernameList,(function(e){return t("el-option",{key:e.id,attrs:{label:e.member_name,value:e.id,disabled:e.disabled}})})),1)],1)],1)],1):e._e(),t("span",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},["upload"==e.type||"close"==e.type||"break"==e.type||"modify"==e.type||"protocol"==e.type?t("el-button",{staticClass:"ps-cancel-btn",on:{click:e.canceDialogHandle}},[e._v("取 消")]):e._e(),t("el-button",{directives:[{name:"loading",rawName:"v-loading",value:e.isLoading,expression:"isLoading"}],staticClass:"ps-btn",attrs:{type:"primary"},on:{click:e.submitDialogHandle}},[e._v("确 定")])],1)])],1)}),[],!1,null,"7723ad5c",null);t.default=h.exports},ddcc:function(e,t,a){"use strict";a.r(t),a.d(t,"TABLE_HEAD_DATA",(function(){return r})),a.d(t,"SEARCH_FORM_SET_DATA",(function(){return i})),a.d(t,"DIC_MERCHANT_STATUS",(function(){return n})),a.d(t,"DIC_MERCHANT_TYPE",(function(){return l})),a.d(t,"DIC_MERCHANT_ID_TYPE",(function(){return o})),a.d(t,"DIC_PERSON_MERCHANT_CATEGORY",(function(){return s})),a.d(t,"DIC_MERCHANT_CONTACT_ID",(function(){return c})),a.d(t,"DIC_CERTIFICATE_TYPE",(function(){return u})),a.d(t,"DIC_ACCOUNT_TYPE",(function(){return d})),a.d(t,"DIC_IS_NOT",(function(){return p})),a.d(t,"UPLOAD_DIALOG_DATA_LIST",(function(){return h})),a.d(t,"PRINT_BANK_TABBLE_SETTING",(function(){return f}));var r=[{label:"二级商户编号",key:"sub_mch_id",width:"140",fixed:"left"},{label:"二级商户名称",key:"sub_mch_name",width:"120"},{label:"二级商户类型",key:"sub_mch_type_alias",width:"120"},{label:"二级商户证件类型",key:"company_cert_type_alias",width:"140"},{label:"二级商户证件有效期结束时间",key:"end_certificate_validity",width:"140"},{label:"法定代表人",key:"contact_name",width:"120"},{label:"法定代表人证件类型",key:"certificate_type_alias",width:"150"},{label:"法定代表人证件编号",key:"certificate_no",width:"150"},{label:"法定代表人证件有效结束时间",key:"fr_cert_end_date",width:"140"},{label:"银行账号",key:"account",width:"120"},{label:"银行账户户名",key:"account_name",width:"120"},{label:"开户银行名称",key:"bank_name",width:"120"},{label:"银行预留手机号",key:"mobile_phone",width:"120"},{label:"账户类型",key:"account_type_alias",width:"120"},{label:"二级商户状态",key:"is_passed_alias",width:"120"},{label:"历史二级商户号",key:"history_sub_mch_ids",type:"slot",slotName:"detail",width:"120"},{label:"签署组织",key:"organization_name",width:"120",type:"slot",slotName:"organizationName"},{label:"签署账号",key:"get_agreement_info",width:"120",type:"slot",slotName:"accountName"},{label:"是否签署协议",key:"is_sign",width:"120",type:"slot",slotName:"isSign"},{label:"操作人",key:"operator",width:"120"},{label:"操作时间",key:"update_time",width:"120"},{label:"操作",key:"operation",type:"slot",slotName:"operation",fixed:"right",width:"200"}],i={sub_mch_id:{type:"input",value:"",label:"二级商户编号",placeholder:"请输入二级商户编号"},sub_mch_name:{type:"input",value:"",label:"二级商户名称",placeholder:"请输入二级商户名称"},is_passed:{type:"select",label:"商户状态",value:[],placeholder:"请选择商户状态",listNameKey:"name",listValueKey:"value",dataList:[]}},n=[{name:"全部",value:"",label:""},{name:"已验证,未审核(不可交易)",value:"0",label:"VERIFIED_NOT_REVIEWED_0"},{name:"已验证,审核通过",value:"1",label:"VERIFIED_REVIEWED"},{name:"已验证,未审核(暂可交易)",value:"2",label:"VERIFIED_NOT_REVIEWED_2"},{name:"未验证,未审核",value:"3",label:"NOT_VERIFIED_NOT_REVIEWED"},{name:"已解约",value:"4",label:"TERMINATED"},{name:"已关闭",value:"5",label:"CLOSED"},{name:"审核拒绝",value:"8",label:"AUDIT_REJECTED"},{name:"驳回",value:"9",label:"TURN_DOWN"}],l=[{name:"个人商户",value:"1",label:"INDIVIDUAL"},{name:"企业",value:"2",label:"ENTERPRISE"},{name:"个体工商户",value:"3",label:"INDIVIDUAL_BUSINESS"},{name:"政府、金融机构及事业单位",value:"4",label:"BUSINESS_UNIT"}],o=[{name:"个体工商户营业执照",value:"610049",label:"INDIVIDUAL_LICENSE"},{name:"企业营业执照",value:"610047",label:"ENTERPRISE_LICENSE"},{name:"组织机构代码",value:"610001",label:"ORGANIZATION_CODE"},{name:"统一社会信用代码",value:"611009",label:"CREDIT_CODE"},{name:"事业单位法人证书",value:"610170",label:"BUSINESS_UNIT_CERTIFICATE"},{name:"社会团体登记证书",value:"610023",label:"SOCIAL_GROUPS_CERTIFICATE"},{name:"民办非企业登记证书",value:"610025",label:"PRIVATE_CERTIFICATE"},{name:"农民专业合作社营业执照",value:"610079",label:"FARMER_LICENSE"},{name:"主管部门颁居民委员会批文",value:"610033",label:"COMMITTEE_APPROVAL"},{name:"政府主管部门批文",value:"610037",label:"GOVERNMENT_APPROVAL"},{name:"财政部门证明",value:"610039",label:"FINANCIAL_PROVE"},{name:"其他机构证件标识",value:"619999",label:"OTHER"}],s=[{name:"有固定经营场所的实体商户",value:"0",label:"FIXED"},{name:"无固定经营场所的实体商户",value:"1",label:"NOT_FIXED"},{name:"网络商户",value:"2",label:"NET"}],c=[{name:"商户信息核实联系人",value:"01",label:"VERIFY_CONTACT"},{name:"商户巡检联系人",value:"02",label:"INSPECTION_CONTACT"},{name:"客户投诉处理联系人",value:"03",label:"COMPLAINT_HANDLING_CONTACT"}],u=[{name:"身份证",value:"110001",label:"ID_CARD"},{name:"临时居民身份证",value:"110003",label:"TEMPORARY_ID_CARD"},{name:"中国人民解放军军人身份证件",value:"110007",label:"MILITARY_ID"},{name:"中国人民武装警察身份证件",value:"110009",label:"POLICE_ID"},{name:"港澳居民来往内地通行证",value:"110019",label:"HONG_KONG_AND_MACAU_PASS"},{name:"台湾居民来往大陆通行证",value:"110021",label:"TAIWAN_PASS"},{name:"中华人民共和国护照",value:"110023",label:"CHINESE_PASSPORT"},{name:"外国护照",value:"110025",label:"FOREIGN_PASSPORT"},{name:"其他证件",value:"119999",label:"OTHER"}],d=[{name:"借记卡",value:"401",label:"DEBIT_CARD"},{name:"企业户",value:"601",label:"ENTERPRISE_HOUSEHOLD"},{name:"二类户",value:"701",label:"CLASS_II_HOUSEHOLDS"},{name:"三类户",value:"702",label:"CLASS_III_HOUSEHOLDS"}],p=[{name:"是",value:!0},{name:"否",value:!1}],h=[{name:"法人身份证人像面照片",required:!0,fileName:"",fileKey:"id_card_face_url",fileUrl:""},{name:"法人身份证国徽面照片",required:!0,fileName:"",fileKey:"id_card_national_emblem_url",fileUrl:""},{name:"法人护照、通行证照片",required:!0,fileName:"",fileKey:"passport_url",fileUrl:""},{name:"个体工商户/企业营业执照照片",required:!0,fileName:"",fileKey:"license_url",fileUrl:""},{name:"辅助证明材料",required:!0,fileName:"",fileKey:"auxiliary_proof_url",fileUrl:""},{name:"政府机关/事业单位/社会组织登记证书照片",required:!0,fileName:"",fileKey:"certificate_url",fileUrl:""},{name:"法定代表人授权函",required:!1,fileName:"",fileKey:"authorization_letter_url",fileUrl:""},{name:"定位证明材料",required:!1,fileName:"",fileKey:"gps_prove_url",fileUrl:""},{name:"固定经营场所证明材料",required:!1,fileName:"",fileKey:"fixed_place_prove_url",fileUrl:""},{name:"合法合规用途证明材料：",required:!1,fileName:"",fileKey:"use_prove_url",fileUrl:""}],f=[{key:"ori_sub_mer_no",label:"*原二级商户号"},{key:"sub_merchant_short_name",label:"*二级商户名称"},{key:"sub_mch_type",label:"*二级商户类型"},{key:"sub_mch_name",label:"*二级商户经营名称"},{key:"service_phone",label:"*二级商户客服电话"},{key:"industry",label:"*二级商户所属行业"},{key:"business_range",label:"二级商户经营范围"},{key:"address",label:"*二级商户实际经营地址"},{key:"company_cert_type",label:"二级商户证件类型"},{key:"company_cert_no",label:"二级商户证件编号"},{key:"end_certificate_validity",label:"二级商户证件有效期"},{key:"sub_mer_class",label:"个人商户类别"},{key:"account",label:"*银行账号"},{key:"account_name",label:"*银行账户户名"},{key:"bank_name",label:"*开户银行名称"},{key:"mobile_phone",label:"*银行预留手机号"},{key:"account_type",label:"*账户类型"},{key:"apply_service",label:"*申请服务"},{key:"sub_mer_contact_name",label:"*联系人姓名"},{key:"mer_mobile_phone",label:"*联系人手机号码"},{key:"sub_mer_contact_cert",label:"*联系人证件号码"},{key:"sub_mer_contact_mail",label:"联系人邮箱"},{key:"sub_mer_contact_type",label:"*商户联系人业务标识"},{key:"contact_name",label:"*法定代表人姓名"},{key:"certificate_type",label:"*法定代表人证件类型"},{key:"certificate_no",label:"*法定代表人证件编号"},{key:"certificate_beg_date",label:"*法定代表人证件有效期开始时间"},{key:"fr_cert_end_date",label:"*法定代表人证件有效期结束时间"},{key:"fr_residence",label:"*法定代表人证件居住地址"},{key:"fr_is_controller",label:"法定代表人是否为受益所有人"},{key:"fr_is_agent",label:"法定代表人是否为实际办理业务人员"},{key:"controller_name",label:"受益所有人姓名"},{key:"controller_cert_type",label:"受益所有人证件类型"},{key:"controller_cert_no",label:"受益所有人证件号码"},{key:"controller_cert_beg_date",label:"受益所有人证件有效期开始时间"},{key:"controller_cert_end_date",label:"受益所有人证件有效期结束时间"},{key:"controller_residence",label:"受益所有人证件居住地址"},{key:"agent_name",label:"授权办理业务人员姓名"},{key:"agent_cert_type",label:"授权办理业务人员证件类型"},{key:"agent_cert_no",label:"授权办理业务人员证件号码"},{key:"agent_cert_beg_date",label:"授权办理业务人员证件有效期开始时间"},{key:"agent_cert_end_date",label:"授权办理业务人员证件有效期结束时间"},{key:"agent_residence",label:"授权办理业务人员证件居住地址"}]},fc23:function(e,t,a){"use strict";a("83f7")}}]);