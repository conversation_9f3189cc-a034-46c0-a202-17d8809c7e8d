(window.webpackJsonp=window.webpackJsonp||[]).push([["view-super-health-system-user-health-records-detail-HealthyLabel"],{"0901":function(t,e,a){},"45e2":function(t,e,a){"use strict";a("0901")},efcf:function(t,e,a){"use strict";a.r(e);var l={props:{formInfoData:{type:Array,default:function(){return[]}}},data:function(){return{labelList:[],formData:{}}},watch:{formInfoData:function(t){var e=this;t.length&&(this.labelList=[],t.forEach((function(t){t.label_name.length&&"taste"!==t.key&&e.labelList.push(t)})))}},mounted:function(){},methods:{}},s=(a("45e2"),a("2877")),n=Object(s.a)(l,(function(){var t=this,e=t._self._c;return e("div",{staticClass:"label-wrapp records-wrapp-bg m-r-20 m-b-20"},[e("div",{staticClass:"p-b-10",staticStyle:{"font-weight":"bold"}},[t._v("标签属性")]),e("div",{staticClass:"label-form"},[e("el-form",{ref:"form",attrs:{size:"mini",model:t.formData,"label-position":"left","label-width":"90px"}},t._l(t.labelList,(function(a,l){return e("el-form-item",{key:l,staticClass:"p-b-10",attrs:{label:a.name}},["ingredient_taboo"===a.key?e("div",t._l(a.label_name,(function(a,l){return e("el-tag",{key:l,staticClass:"m-r-10",attrs:{size:"small",effect:"plain",type:"warning",color:"#fff"}},[e("i",{staticClass:"el-icon-warning ps-i"}),t._v(" "+t._s(a)+" ")])})),1):"taste"===a.key?e("div",t._l(a.label_name,(function(a,l){return e("el-tag",{key:l,staticClass:"m-r-10",attrs:{size:"small",effect:a.is_have?"plain":"dark",type:"info"}},[e("div",{style:{color:a.is_have?"":"#fff"}},[e("span",[t._v(t._s(a.name))]),a.count?e("span",[t._v("*"+t._s(a.count))]):t._e()])])})),1):e("div",t._l(a.label_name,(function(a,l){return e("el-tag",{key:l,staticClass:"m-r-10",attrs:{size:"small",effect:"plain",type:"info",color:"#fff"}},[t._v(" "+t._s(a)+" ")])})),1)])})),1)],1)])}),[],!1,null,"6486084a",null);e.default=n.exports}}]);