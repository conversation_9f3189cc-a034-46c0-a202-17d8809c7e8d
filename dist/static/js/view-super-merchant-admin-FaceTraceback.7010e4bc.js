(window.webpackJsonp=window.webpackJsonp||[]).push([["view-super-merchant-admin-FaceTraceback"],{68849:function(t,e,r){"use strict";r("b939")},a427:function(t,e,r){"use strict";r.r(e);var n=r("ed08");function a(t){return(a="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function i(){/*! regenerator-runtime -- Copyright (c) 2014-present, Facebook, Inc. -- license (MIT): https://github.com/facebook/regenerator/blob/main/LICENSE */i=function(){return e};var t,e={},r=Object.prototype,n=r.hasOwnProperty,o=Object.defineProperty||function(t,e,r){t[e]=r.value},s="function"==typeof Symbol?Symbol:{},c=s.iterator||"@@iterator",l=s.asyncIterator||"@@asyncIterator",u=s.toStringTag||"@@toStringTag";function f(t,e,r){return Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}),t[e]}try{f({},"")}catch(t){f=function(t,e,r){return t[e]=r}}function h(t,e,r,n){var a=e&&e.prototype instanceof y?e:y,i=Object.create(a.prototype),s=new E(n||[]);return o(i,"_invoke",{value:P(t,r,s)}),i}function p(t,e,r){try{return{type:"normal",arg:t.call(e,r)}}catch(t){return{type:"throw",arg:t}}}e.wrap=h;var g="suspendedStart",d="executing",m="completed",v={};function y(){}function b(){}function w(){}var S={};f(S,c,(function(){return this}));var L=Object.getPrototypeOf,x=L&&L(L(N([])));x&&x!==r&&n.call(x,c)&&(S=x);var _=w.prototype=y.prototype=Object.create(S);function k(t){["next","throw","return"].forEach((function(e){f(t,e,(function(t){return this._invoke(e,t)}))}))}function O(t,e){function r(i,o,s,c){var l=p(t[i],t,o);if("throw"!==l.type){var u=l.arg,f=u.value;return f&&"object"==a(f)&&n.call(f,"__await")?e.resolve(f.__await).then((function(t){r("next",t,s,c)}),(function(t){r("throw",t,s,c)})):e.resolve(f).then((function(t){u.value=t,s(u)}),(function(t){return r("throw",t,s,c)}))}c(l.arg)}var i;o(this,"_invoke",{value:function(t,n){function a(){return new e((function(e,a){r(t,n,e,a)}))}return i=i?i.then(a,a):a()}})}function P(e,r,n){var a=g;return function(i,o){if(a===d)throw Error("Generator is already running");if(a===m){if("throw"===i)throw o;return{value:t,done:!0}}for(n.method=i,n.arg=o;;){var s=n.delegate;if(s){var c=j(s,n);if(c){if(c===v)continue;return c}}if("next"===n.method)n.sent=n._sent=n.arg;else if("throw"===n.method){if(a===g)throw a=m,n.arg;n.dispatchException(n.arg)}else"return"===n.method&&n.abrupt("return",n.arg);a=d;var l=p(e,r,n);if("normal"===l.type){if(a=n.done?m:"suspendedYield",l.arg===v)continue;return{value:l.arg,done:n.done}}"throw"===l.type&&(a=m,n.method="throw",n.arg=l.arg)}}}function j(e,r){var n=r.method,a=e.iterator[n];if(a===t)return r.delegate=null,"throw"===n&&e.iterator.return&&(r.method="return",r.arg=t,j(e,r),"throw"===r.method)||"return"!==n&&(r.method="throw",r.arg=new TypeError("The iterator does not provide a '"+n+"' method")),v;var i=p(a,e.iterator,r.arg);if("throw"===i.type)return r.method="throw",r.arg=i.arg,r.delegate=null,v;var o=i.arg;return o?o.done?(r[e.resultName]=o.value,r.next=e.nextLoc,"return"!==r.method&&(r.method="next",r.arg=t),r.delegate=null,v):o:(r.method="throw",r.arg=new TypeError("iterator result is not an object"),r.delegate=null,v)}function C(t){var e={tryLoc:t[0]};1 in t&&(e.catchLoc=t[1]),2 in t&&(e.finallyLoc=t[2],e.afterLoc=t[3]),this.tryEntries.push(e)}function F(t){var e=t.completion||{};e.type="normal",delete e.arg,t.completion=e}function E(t){this.tryEntries=[{tryLoc:"root"}],t.forEach(C,this),this.reset(!0)}function N(e){if(e||""===e){var r=e[c];if(r)return r.call(e);if("function"==typeof e.next)return e;if(!isNaN(e.length)){var i=-1,o=function r(){for(;++i<e.length;)if(n.call(e,i))return r.value=e[i],r.done=!1,r;return r.value=t,r.done=!0,r};return o.next=o}}throw new TypeError(a(e)+" is not iterable")}return b.prototype=w,o(_,"constructor",{value:w,configurable:!0}),o(w,"constructor",{value:b,configurable:!0}),b.displayName=f(w,u,"GeneratorFunction"),e.isGeneratorFunction=function(t){var e="function"==typeof t&&t.constructor;return!!e&&(e===b||"GeneratorFunction"===(e.displayName||e.name))},e.mark=function(t){return Object.setPrototypeOf?Object.setPrototypeOf(t,w):(t.__proto__=w,f(t,u,"GeneratorFunction")),t.prototype=Object.create(_),t},e.awrap=function(t){return{__await:t}},k(O.prototype),f(O.prototype,l,(function(){return this})),e.AsyncIterator=O,e.async=function(t,r,n,a,i){void 0===i&&(i=Promise);var o=new O(h(t,r,n,a),i);return e.isGeneratorFunction(r)?o:o.next().then((function(t){return t.done?t.value:o.next()}))},k(_),f(_,u,"Generator"),f(_,c,(function(){return this})),f(_,"toString",(function(){return"[object Generator]"})),e.keys=function(t){var e=Object(t),r=[];for(var n in e)r.push(n);return r.reverse(),function t(){for(;r.length;){var n=r.pop();if(n in e)return t.value=n,t.done=!1,t}return t.done=!0,t}},e.values=N,E.prototype={constructor:E,reset:function(e){if(this.prev=0,this.next=0,this.sent=this._sent=t,this.done=!1,this.delegate=null,this.method="next",this.arg=t,this.tryEntries.forEach(F),!e)for(var r in this)"t"===r.charAt(0)&&n.call(this,r)&&!isNaN(+r.slice(1))&&(this[r]=t)},stop:function(){this.done=!0;var t=this.tryEntries[0].completion;if("throw"===t.type)throw t.arg;return this.rval},dispatchException:function(e){if(this.done)throw e;var r=this;function a(n,a){return s.type="throw",s.arg=e,r.next=n,a&&(r.method="next",r.arg=t),!!a}for(var i=this.tryEntries.length-1;i>=0;--i){var o=this.tryEntries[i],s=o.completion;if("root"===o.tryLoc)return a("end");if(o.tryLoc<=this.prev){var c=n.call(o,"catchLoc"),l=n.call(o,"finallyLoc");if(c&&l){if(this.prev<o.catchLoc)return a(o.catchLoc,!0);if(this.prev<o.finallyLoc)return a(o.finallyLoc)}else if(c){if(this.prev<o.catchLoc)return a(o.catchLoc,!0)}else{if(!l)throw Error("try statement without catch or finally");if(this.prev<o.finallyLoc)return a(o.finallyLoc)}}}},abrupt:function(t,e){for(var r=this.tryEntries.length-1;r>=0;--r){var a=this.tryEntries[r];if(a.tryLoc<=this.prev&&n.call(a,"finallyLoc")&&this.prev<a.finallyLoc){var i=a;break}}i&&("break"===t||"continue"===t)&&i.tryLoc<=e&&e<=i.finallyLoc&&(i=null);var o=i?i.completion:{};return o.type=t,o.arg=e,i?(this.method="next",this.next=i.finallyLoc,v):this.complete(o)},complete:function(t,e){if("throw"===t.type)throw t.arg;return"break"===t.type||"continue"===t.type?this.next=t.arg:"return"===t.type?(this.rval=this.arg=t.arg,this.method="return",this.next="end"):"normal"===t.type&&e&&(this.next=e),v},finish:function(t){for(var e=this.tryEntries.length-1;e>=0;--e){var r=this.tryEntries[e];if(r.finallyLoc===t)return this.complete(r.completion,r.afterLoc),F(r),v}},catch:function(t){for(var e=this.tryEntries.length-1;e>=0;--e){var r=this.tryEntries[e];if(r.tryLoc===t){var n=r.completion;if("throw"===n.type){var a=n.arg;F(r)}return a}}throw Error("illegal catch attempt")},delegateYield:function(e,r,n){return this.delegate={iterator:N(e),resultName:r,nextLoc:n},"next"===this.method&&(this.arg=t),v}},e}function o(t,e){return function(t){if(Array.isArray(t))return t}(t)||function(t,e){var r=null==t?null:"undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(null!=r){var n,a,i,o,s=[],c=!0,l=!1;try{if(i=(r=r.call(t)).next,0===e){if(Object(r)!==r)return;c=!1}else for(;!(c=(n=i.call(r)).done)&&(s.push(n.value),s.length!==e);c=!0);}catch(t){l=!0,a=t}finally{try{if(!c&&null!=r.return&&(o=r.return(),Object(o)!==o))return}finally{if(l)throw a}}return s}}(t,e)||function(t,e){if(t){if("string"==typeof t)return s(t,e);var r={}.toString.call(t).slice(8,-1);return"Object"===r&&t.constructor&&(r=t.constructor.name),"Map"===r||"Set"===r?Array.from(t):"Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r)?s(t,e):void 0}}(t,e)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function s(t,e){(null==e||e>t.length)&&(e=t.length);for(var r=0,n=Array(e);r<e;r++)n[r]=t[r];return n}function c(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter((function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable}))),r.push.apply(r,n)}return r}function l(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?c(Object(r),!0).forEach((function(e){u(t,e,r[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):c(Object(r)).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))}))}return t}function u(t,e,r){return(e=function(t){var e=function(t,e){if("object"!=a(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=a(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==a(e)?e:e+""}(e))in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function f(t,e,r,n,a,i,o){try{var s=t[i](o),c=s.value}catch(t){return void r(t)}s.done?e(c):Promise.resolve(c).then(n,a)}var h={name:"FaceTraceback",data:function(){return{isLoading:!1,pageSize:5,totalCount:0,currentPage:1,tableData:[],tableDataClone:[],tableSettings:[{label:"排序",key:"index"},{label:"近似人脸",key:"face_url",type:"slot",slotName:"face"},{label:"人员编号",key:"member_card"},{label:"姓名",key:"card_name"},{label:"手机号",key:"card_phone"},{label:"性别",key:"gender",type:"slot",slotName:"sex"},{label:"识别分数",key:"score",type:"slot",slotName:"score"},{label:"操作",key:"operation",type:"slot",slotName:"operation",fixed:"right",width:"100"}],searchFormSetting:{company_id:{type:"CompanySelect",value:[],label:"组织名称",dataList:[],multiple:!1,checkStrictly:!0,clearable:!0,companyOpts:{label:"name",value:"company"},role:"super"},count:{type:"input",value:"",label:"人脸回溯数量",placeholder:"请输入查询数量（上限50条）"}},fileListsUpload:[],isShowSearchBtn:!1,isShowResetBtn:!1,uploadTipRightTop:"上传：查询人脸",uploadTipRightBottom:"仅支持jpg、png、bmp格式，大小不超过10M",isShowPopImg:!1,faceImgUrl:"",isUploading:!1,isShowCollapse:!1}},created:function(){this.initLoad()},methods:{refreshHandle:function(){this.currentPage=1,this.resetFormFace(),this.tableData=[],this.initLoad()},initLoad:function(){},clickConfirmHandle:function(){if(0!==this.searchFormSetting.company_id.value.length)if(0!==this.searchFormSetting.count.value.length&&"0"!==this.searchFormSetting.count.value){this.searchFormSetting.count.value.length>0&&!/^\d+$/.test(this.searchFormSetting.count.value)?this.$message.error("人脸回溯数量必须为整数"):parseInt(this.searchFormSetting.count.value)>50?this.$message.error("人脸回溯数量一次查询的数量必须低于50"):0!==this.fileListsUpload.length?this.getFaceList():this.$message.error("请选择查询人脸")}else this.$message.error("请输入人脸回溯数量");else this.$message.error("请选择组织名称")},showDialogHandle:function(t){var e=t.member_card;this.$router.push({name:"FaceTracebackDetail",query:{id:e}})},onPaginationChange:function(t){this.currentPage=t.current,this.pageSize=t.pageSize;var e=this.tableDataClone.slice((this.currentPage-1)*this.pageSize,this.currentPage*this.pageSize);this.tableData=Object(n.f)(e)},beforeUpload:function(t){return[".jpg",".png",".bmp"].includes(this.getSuffix(t.name))?t.size/1024/1024<10?void(this.isUploading=!0):(this.$message.error("上传图片大小不能超过 10MB!"),!1):(this.$message.error("上传图片只能是 jpg/png 格式"),!1)},getSuffix:function(t){var e=t.lastIndexOf("."),r="";return-1!==e&&(r=t.substring(e)),r},getFileLists:function(t){this.isUploading=!1,this.fileListsUpload=Object(n.f)(t)},getFaceList:function(){var t=this;return function(t){return function(){var e=this,r=arguments;return new Promise((function(n,a){var i=t.apply(e,r);function o(t){f(i,n,a,o,s,"next",t)}function s(t){f(i,n,a,o,s,"throw",t)}o(void 0)}))}}(i().mark((function e(){var r,a,s,c,u,f;return i().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(!t.isLoading){e.next=2;break}return e.abrupt("return");case 2:return t.isLoading=!0,r=l(l({},t.formatQueryParams(t.searchFormSetting)),{},{face_url:t.fileListsUpload[0].url}),e.next=6,Object(n.X)(t.$apis.apiBackgroundAdminFaceSearchFaceTracebackPost(r));case 6:if(a=e.sent,s=o(a,2),c=s[0],u=s[1],t.isLoading=!1,!c){e.next=15;break}return t.$message.error(c.message),e.abrupt("return");case 15:0===u.code?(f=u.data||[],Array.isArray(f)&&f.length>0&&f.map((function(t,e){return t.index=e+1,t})),t.tableData=Object(n.f)(f.slice(0,t.pageSize)),t.tableDataClone=Object(n.f)(f),t.totalCount=f.length):t.$message.error(u.msg);case 16:case"end":return e.stop()}}),e)})))()},formatQueryParams:function(t){var e={};for(var r in t)""!==t[r].value&&null!==t[r].value&&0!==t[r].value.length&&("count"===r?e[r]=parseInt(t[r].value):"select_time"!==r?e[r]=t[r].value:t[r].value.length>0&&(e.start_time=t[r].value[0],e.end_time=t[r].value[1]));return e},clearFileHandle:function(){this.$refs.faceFileRef.clearHandle(),this.fileListsUpload=[]},getSexName:function(t){switch(t){case"MAN":return"男";case"WOMEN":return"女";case"OTHER":return"其他"}},resetFormFace:function(){this.$refs.searchRef&&Reflect.has(this.$refs.searchRef,"resetForm")&&this.$refs.searchRef.resetForm(),this.clearFileHandle()},handlerImgClick:function(t){this.faceImgUrl!==t?(this.faceImgUrl=t,this.isShowPopImg=!0):this.isShowPopImg=!this.isShowPopImg},uploadError:function(t){this.isUploading=!1},closeCard:function(){this.isShowPopImg=!1}}},p=(r("68849"),r("2877")),g=Object(p.a)(h,(function(){var t=this,e=t._self._c;return e("div",{staticClass:"container-wrapper"},[e("refresh-tool",{on:{refreshPage:t.refreshHandle}}),e("search-form",{ref:"searchRef",attrs:{"form-setting":t.searchFormSetting,"label-width":"105px",isShowSearchBtn:t.isShowSearchBtn,isShowResetBtn:t.isShowResetBtn,isShowCollapse:t.isShowCollapse}},[e("div",{attrs:{slot:"customBtn"},slot:"customBtn"},[e("el-button",{staticClass:"ps-origin-btn search-h-r-btn",attrs:{disabled:t.isLoading,type:"primary",size:"mini"},on:{click:t.clickConfirmHandle}},[t._v("开始查询")]),e("el-button",{staticClass:"search-h-r-btn ps-plain-btn",attrs:{disabled:t.isLoading,size:"mini"},on:{click:t.resetFormFace}},[t._v("重置")])],1),e("div",{staticClass:"face_trace",attrs:{slot:"append"},slot:"append"},[e("el-form-item",{attrs:{label:"查询人脸"}},[e("file-upload",{directives:[{name:"loading",rawName:"v-loading",value:t.isUploading,expression:"isUploading"}],ref:"faceFileRef",class:["avatar-uploader",0==t.fileListsUpload.length?"border-gray-1":""],attrs:{fileList:t.fileListsUpload,type:"enclosure",limit:1,"before-upload":t.beforeUpload,"show-file-list":!1,prefix:"tmp"},on:{fileLists:t.getFileLists,uploadError:t.uploadError}},[t.fileListsUpload.length?e("img",{staticClass:"avatar",attrs:{src:t.fileListsUpload[0].url},on:{click:t.clearFileHandle}}):e("i",{staticClass:"el-icon-plus avatar-uploader-icon"})])],1),e("el-form-item",[e("div",[e("div",{staticClass:"m-t-25 color-gray"},[t._v(" "+t._s(t.uploadTipRightTop)+" ")]),e("div",{staticClass:"color-gray"},[t._v(" "+t._s(t.uploadTipRightBottom)+" ")])])])],1)]),t.isShowPopImg?e("el-card",{directives:[{name:"drag",rawName:"v-drag"}],staticClass:"box-card"},[e("div",{staticClass:"flex-between m-b-10"},[e("span",[t._v("人脸照片")]),e("i",{staticClass:"el-icon-close",staticStyle:{float:"right"},on:{click:t.closeCard}})]),e("img",{staticClass:"face-img-big",attrs:{src:t.faceImgUrl}})]):t._e(),e("div",{staticClass:"table-wrapper"},[t._m(0),e("div",{staticClass:"table-content"},[e("el-table",{directives:[{name:"loading",rawName:"v-loading",value:t.isLoading,expression:"isLoading"}],ref:"tableData",staticStyle:{width:"100%"},attrs:{data:t.tableData,height:"460",stripe:"","header-row-class-name":"ps-table-header-row"}},t._l(t.tableSettings,(function(r){return e("table-column",{key:r.key,attrs:{col:r},scopedSlots:t._u([{key:"face",fn:function(r){var n=r.row;return[e("div",[e("img",{staticClass:"face-img",attrs:{src:n.face_url,alt:""},on:{click:function(e){return t.handlerImgClick(n.face_url)}}})])]}},{key:"sex",fn:function(e){var r=e.row;return[t._v(" "+t._s(t.getSexName(r.gender))+" ")]}},{key:"score",fn:function(e){var r=e.row;return[t._v(" "+t._s(t._f("formatScoreNotRounding")(r.score))+" ")]}},{key:"operation",fn:function(r){var n=r.row;return[e("el-button",{directives:[{name:"permission",rawName:"v-permission",value:["background.admin.face_traceback"],expression:"['background.admin.face_traceback']"}],staticClass:"ps-text",attrs:{type:"text",size:"small"},on:{click:function(e){return t.showDialogHandle(n)}}},[t._v("查看")])]}}],null,!0)})})),1)],1),e("div",{staticClass:"block ps-pagination",staticStyle:{"text-align":"right","padding-top":"20px"}},[e("pagination",{attrs:{onPaginationChange:t.onPaginationChange,"current-page":t.currentPage,"page-size":t.pageSize,layout:"total, prev, pager, next, jumper",total:t.totalCount},on:{"update:currentPage":function(e){t.currentPage=e},"update:current-page":function(e){t.currentPage=e},"update:pageSize":function(e){t.pageSize=e},"update:page-size":function(e){t.pageSize=e}}})],1)])],1)}),[function(){var t=this._self._c;return t("div",{staticClass:"table-header"},[t("div",{staticClass:"table-title"},[this._v("数据列表")])])}],!1,null,"9447ad76",null);e.default=g.exports},b939:function(t,e,r){}}]);