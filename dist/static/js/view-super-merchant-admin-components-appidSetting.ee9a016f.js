(window.webpackJsonp=window.webpackJsonp||[]).push([["view-super-merchant-admin-components-appidSetting"],{3421:function(t,e,r){"use strict";r("813f")},"813f":function(t,e,r){t.exports={menuText:"rgba(35,40,45,.7)",menuActiveText:"#23282d",subMenuActiveText:"#23282d",menuBg:"#fff",menuHover:"#edf1f5",subMenuBg:"#fff",subMenuHover:"rgba(0,0,0,.1)",sideBarWidth:"240px"}},e512:function(t,e,r){"use strict";r.r(e);var n=r("ed08"),a=r("b2e5"),i=r.n(a);function o(t){return(o="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function s(){/*! regenerator-runtime -- Copyright (c) 2014-present, Facebook, Inc. -- license (MIT): https://github.com/facebook/regenerator/blob/main/LICENSE */s=function(){return e};var t,e={},r=Object.prototype,n=r.hasOwnProperty,a=Object.defineProperty||function(t,e,r){t[e]=r.value},i="function"==typeof Symbol?Symbol:{},c=i.iterator||"@@iterator",u=i.asyncIterator||"@@asyncIterator",l=i.toStringTag||"@@toStringTag";function p(t,e,r){return Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}),t[e]}try{p({},"")}catch(t){p=function(t,e,r){return t[e]=r}}function f(t,e,r,n){var i=e&&e.prototype instanceof v?e:v,o=Object.create(i.prototype),s=new E(n||[]);return a(o,"_invoke",{value:S(t,r,s)}),o}function d(t,e,r){try{return{type:"normal",arg:t.call(e,r)}}catch(t){return{type:"throw",arg:t}}}e.wrap=f;var m="suspendedStart",h="executing",y="completed",g={};function v(){}function b(){}function w(){}var x={};p(x,c,(function(){return this}));var k=Object.getPrototypeOf,L=k&&k(k($([])));L&&L!==r&&n.call(L,c)&&(x=L);var _=w.prototype=v.prototype=Object.create(x);function O(t){["next","throw","return"].forEach((function(e){p(t,e,(function(t){return this._invoke(e,t)}))}))}function D(t,e){function r(a,i,s,c){var u=d(t[a],t,i);if("throw"!==u.type){var l=u.arg,p=l.value;return p&&"object"==o(p)&&n.call(p,"__await")?e.resolve(p.__await).then((function(t){r("next",t,s,c)}),(function(t){r("throw",t,s,c)})):e.resolve(p).then((function(t){l.value=t,s(l)}),(function(t){return r("throw",t,s,c)}))}c(u.arg)}var i;a(this,"_invoke",{value:function(t,n){function a(){return new e((function(e,a){r(t,n,e,a)}))}return i=i?i.then(a,a):a()}})}function S(e,r,n){var a=m;return function(i,o){if(a===h)throw Error("Generator is already running");if(a===y){if("throw"===i)throw o;return{value:t,done:!0}}for(n.method=i,n.arg=o;;){var s=n.delegate;if(s){var c=j(s,n);if(c){if(c===g)continue;return c}}if("next"===n.method)n.sent=n._sent=n.arg;else if("throw"===n.method){if(a===m)throw a=y,n.arg;n.dispatchException(n.arg)}else"return"===n.method&&n.abrupt("return",n.arg);a=h;var u=d(e,r,n);if("normal"===u.type){if(a=n.done?y:"suspendedYield",u.arg===g)continue;return{value:u.arg,done:n.done}}"throw"===u.type&&(a=y,n.method="throw",n.arg=u.arg)}}}function j(e,r){var n=r.method,a=e.iterator[n];if(a===t)return r.delegate=null,"throw"===n&&e.iterator.return&&(r.method="return",r.arg=t,j(e,r),"throw"===r.method)||"return"!==n&&(r.method="throw",r.arg=new TypeError("The iterator does not provide a '"+n+"' method")),g;var i=d(a,e.iterator,r.arg);if("throw"===i.type)return r.method="throw",r.arg=i.arg,r.delegate=null,g;var o=i.arg;return o?o.done?(r[e.resultName]=o.value,r.next=e.nextLoc,"return"!==r.method&&(r.method="next",r.arg=t),r.delegate=null,g):o:(r.method="throw",r.arg=new TypeError("iterator result is not an object"),r.delegate=null,g)}function A(t){var e={tryLoc:t[0]};1 in t&&(e.catchLoc=t[1]),2 in t&&(e.finallyLoc=t[2],e.afterLoc=t[3]),this.tryEntries.push(e)}function I(t){var e=t.completion||{};e.type="normal",delete e.arg,t.completion=e}function E(t){this.tryEntries=[{tryLoc:"root"}],t.forEach(A,this),this.reset(!0)}function $(e){if(e||""===e){var r=e[c];if(r)return r.call(e);if("function"==typeof e.next)return e;if(!isNaN(e.length)){var a=-1,i=function r(){for(;++a<e.length;)if(n.call(e,a))return r.value=e[a],r.done=!1,r;return r.value=t,r.done=!0,r};return i.next=i}}throw new TypeError(o(e)+" is not iterable")}return b.prototype=w,a(_,"constructor",{value:w,configurable:!0}),a(w,"constructor",{value:b,configurable:!0}),b.displayName=p(w,l,"GeneratorFunction"),e.isGeneratorFunction=function(t){var e="function"==typeof t&&t.constructor;return!!e&&(e===b||"GeneratorFunction"===(e.displayName||e.name))},e.mark=function(t){return Object.setPrototypeOf?Object.setPrototypeOf(t,w):(t.__proto__=w,p(t,l,"GeneratorFunction")),t.prototype=Object.create(_),t},e.awrap=function(t){return{__await:t}},O(D.prototype),p(D.prototype,u,(function(){return this})),e.AsyncIterator=D,e.async=function(t,r,n,a,i){void 0===i&&(i=Promise);var o=new D(f(t,r,n,a),i);return e.isGeneratorFunction(r)?o:o.next().then((function(t){return t.done?t.value:o.next()}))},O(_),p(_,l,"Generator"),p(_,c,(function(){return this})),p(_,"toString",(function(){return"[object Generator]"})),e.keys=function(t){var e=Object(t),r=[];for(var n in e)r.push(n);return r.reverse(),function t(){for(;r.length;){var n=r.pop();if(n in e)return t.value=n,t.done=!1,t}return t.done=!0,t}},e.values=$,E.prototype={constructor:E,reset:function(e){if(this.prev=0,this.next=0,this.sent=this._sent=t,this.done=!1,this.delegate=null,this.method="next",this.arg=t,this.tryEntries.forEach(I),!e)for(var r in this)"t"===r.charAt(0)&&n.call(this,r)&&!isNaN(+r.slice(1))&&(this[r]=t)},stop:function(){this.done=!0;var t=this.tryEntries[0].completion;if("throw"===t.type)throw t.arg;return this.rval},dispatchException:function(e){if(this.done)throw e;var r=this;function a(n,a){return s.type="throw",s.arg=e,r.next=n,a&&(r.method="next",r.arg=t),!!a}for(var i=this.tryEntries.length-1;i>=0;--i){var o=this.tryEntries[i],s=o.completion;if("root"===o.tryLoc)return a("end");if(o.tryLoc<=this.prev){var c=n.call(o,"catchLoc"),u=n.call(o,"finallyLoc");if(c&&u){if(this.prev<o.catchLoc)return a(o.catchLoc,!0);if(this.prev<o.finallyLoc)return a(o.finallyLoc)}else if(c){if(this.prev<o.catchLoc)return a(o.catchLoc,!0)}else{if(!u)throw Error("try statement without catch or finally");if(this.prev<o.finallyLoc)return a(o.finallyLoc)}}}},abrupt:function(t,e){for(var r=this.tryEntries.length-1;r>=0;--r){var a=this.tryEntries[r];if(a.tryLoc<=this.prev&&n.call(a,"finallyLoc")&&this.prev<a.finallyLoc){var i=a;break}}i&&("break"===t||"continue"===t)&&i.tryLoc<=e&&e<=i.finallyLoc&&(i=null);var o=i?i.completion:{};return o.type=t,o.arg=e,i?(this.method="next",this.next=i.finallyLoc,g):this.complete(o)},complete:function(t,e){if("throw"===t.type)throw t.arg;return"break"===t.type||"continue"===t.type?this.next=t.arg:"return"===t.type?(this.rval=this.arg=t.arg,this.method="return",this.next="end"):"normal"===t.type&&e&&(this.next=e),g},finish:function(t){for(var e=this.tryEntries.length-1;e>=0;--e){var r=this.tryEntries[e];if(r.finallyLoc===t)return this.complete(r.completion,r.afterLoc),I(r),g}},catch:function(t){for(var e=this.tryEntries.length-1;e>=0;--e){var r=this.tryEntries[e];if(r.tryLoc===t){var n=r.completion;if("throw"===n.type){var a=n.arg;I(r)}return a}}throw Error("illegal catch attempt")},delegateYield:function(e,r,n){return this.delegate={iterator:$(e),resultName:r,nextLoc:n},"next"===this.method&&(this.arg=t),g}},e}function c(t,e){return function(t){if(Array.isArray(t))return t}(t)||function(t,e){var r=null==t?null:"undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(null!=r){var n,a,i,o,s=[],c=!0,u=!1;try{if(i=(r=r.call(t)).next,0===e){if(Object(r)!==r)return;c=!1}else for(;!(c=(n=i.call(r)).done)&&(s.push(n.value),s.length!==e);c=!0);}catch(t){u=!0,a=t}finally{try{if(!c&&null!=r.return&&(o=r.return(),Object(o)!==o))return}finally{if(u)throw a}}return s}}(t,e)||function(t,e){if(t){if("string"==typeof t)return u(t,e);var r={}.toString.call(t).slice(8,-1);return"Object"===r&&t.constructor&&(r=t.constructor.name),"Map"===r||"Set"===r?Array.from(t):"Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r)?u(t,e):void 0}}(t,e)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function u(t,e){(null==e||e>t.length)&&(e=t.length);for(var r=0,n=Array(e);r<e;r++)n[r]=t[r];return n}function l(t,e,r,n,a,i,o){try{var s=t[i](o),c=s.value}catch(t){return void r(t)}s.done?e(c):Promise.resolve(c).then(n,a)}function p(t){return function(){var e=this,r=arguments;return new Promise((function(n,a){var i=t.apply(e,r);function o(t){l(i,n,a,o,s,"next",t)}function s(t){l(i,n,a,o,s,"throw",t)}o(void 0)}))}}var f={name:"SuperBindAppid",props:{type:String,infoData:{type:Object,default:function(){return{}}},organizationData:Object,restoreHandle:Function},components:{qrcode:i.a},data:function(){return{formOperate:"detail",isLoading:!1,formData:{appid:"",secret_key:"",menuList:[],templateId:""},appid:"",secret_key:"",auth_time:"",appidList:[],formDataRuls:{appid:[{required:!1,message:"请先输入appid",trigger:"blur"}],secret_key:[{required:!1,message:"请先输入secret_key",trigger:"blur"}]},qrcodeOptions:{width:256,height:256},qrcodeValue:"pushi",allMenuList:[]}},computed:{checkIsFormStatus:function(){var t=!1;switch(this.formOperate){case"detail":t=!1;break;case"modify":t=!0}return t}},watch:{infoData:function(t){this.appid=t.appid}},created:function(){},mounted:function(){this.initLoad()},methods:{initLoad:function(){this.formData.appid=this.infoData.appid,this.formData.secret_key=this.infoData.secret_key,this.formData.templateId=this.infoData.template_id,this.getOrgIsBindAppid(),this.getAppPermission()},refreshHandle:function(){this.initLoad()},searchHandle:Object(n.d)((function(){}),300),changeOperate:function(){"modify"!==this.formOperate?this.formOperate="modify":this.formOperate="detail"},getAppidList:function(){var t=this;return p(s().mark((function e(){var r,a,i,o;return s().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return t.isLoading=!0,e.next=3,Object(n.X)(t.$apis.apiBackgroundAdminOrganizationGetAppidListPost());case 3:if(r=e.sent,a=c(r,2),i=a[0],o=a[1],t.isLoading=!1,!i){e.next=11;break}return t.$message.error(i.message),e.abrupt("return");case 11:0===o.code?t.appidList=o.data:t.$message.error(o.msg);case 12:case"end":return e.stop()}}),e)})))()},getOrgIsBindAppid:function(){var t=this;return p(s().mark((function e(){var r,a,i,o;return s().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return t.isLoading=!0,e.next=3,Object(n.X)(t.$apis.apiBackgroundAdminOrganizationGetOrgAppidPost({id:t.organizationData.id,company:t.organizationData.company}));case 3:if(r=e.sent,a=c(r,2),i=a[0],o=a[1],t.isLoading=!1,!i){e.next=11;break}return t.$message.error(i.message),e.abrupt("return");case 11:0===o.code?(t.formData.appid=o.data.appid,t.formData.secret_key=o.data.secret_key,t.qrcodeValue=o.data.booking_url,t.formData.menuList=o.data.app_permission):t.$message.error(o.msg);case 12:case"end":return e.stop()}}),e)})))()},saveAppidHandle:function(){var t=this;return p(s().mark((function e(){return s().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(!t.isLoading){e.next=2;break}return e.abrupt("return");case 2:t.$refs.appidRef.validate((function(e){e&&t.modifyOrganization()}));case 3:case"end":return e.stop()}}),e)})))()},modifyOrganization:function(){var t=this;return p(s().mark((function e(){var r,a,i,o,u;return s().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return r={id:t.organizationData.id,appid:t.formData.appid,secret_key:t.formData.secret_key,company:t.organizationData.company,app_permission:t.formData.menuList},t.formData.templateId&&(r.template_id=t.formData.templateId),t.isLoading=!0,e.next=5,Object(n.X)(t.$apis.apiBackgroundAdminOrganizationModifyPost(r));case 5:if(a=e.sent,i=c(a,2),o=i[0],u=i[1],t.isLoading=!1,!o){e.next=13;break}return t.$message.error(o.message),e.abrupt("return");case 13:0===u.code?(t.$message.success("修改成功"),t.formOperate="detail",t.restoreHandle(t.type,t.formOperate)):t.$message.error(u.msg);case 14:case"end":return e.stop()}}),e)})))()},clipboardSuccess:function(){this.$message({message:"复制成功",type:"success",duration:1500})},getAppPermission:function(){var t=this;return p(s().mark((function e(){var r,a,i,o;return s().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return t.isLoading=!0,e.next=3,Object(n.X)(t.$apis.apiBackgroundAdminOrganizationGetAppPermissionsPost());case 3:if(r=e.sent,a=c(r,2),i=a[0],o=a[1],t.isLoading=!1,!i){e.next=11;break}return t.$message.error(i.message),e.abrupt("return");case 11:0===o.code?t.allMenuList=o.data[0].children:t.$message.error(o.msg);case 12:case"end":return e.stop()}}),e)})))()}}},d=(r("3421"),r("2877")),m=Object(d.a)(f,(function(){var t=this,e=t._self._c;return e("div",{directives:[{name:"loading",rawName:"v-loading",value:t.isLoading,expression:"isLoading"}],staticClass:"container-wrapper bindappid"},[e("div",{staticClass:"l-title clearfix"},[e("span",{staticClass:"float-l min-title-h"},[t._v("绑定公众号")]),"detail"===t.formOperate?e("el-button",{directives:[{name:"permission",rawName:"v-permission",value:["background.admin.organization.modify"],expression:"['background.admin.organization.modify']"}],staticClass:"float-r",attrs:{size:"mini"},on:{click:t.changeOperate}},[t._v("编辑")]):t._e()],1),e("div",{staticClass:"appid-box"},[e("el-form",{ref:"appidRef",attrs:{rules:t.formDataRuls,model:t.formData,size:"small","label-width":"100px"}},[e("el-form-item",{attrs:{label:"appid",prop:"appid"}},[e("el-input",{staticStyle:{"max-width":"300px"},attrs:{disabled:!t.checkIsFormStatus,placeholder:"请输入appid"},model:{value:t.formData.appid,callback:function(e){t.$set(t.formData,"appid",e)},expression:"formData.appid"}})],1),e("el-form-item",{attrs:{label:"secret_key",prop:"secret_key"}},[e("el-input",{staticStyle:{"max-width":"300px"},attrs:{disabled:!t.checkIsFormStatus,placeholder:"请输入appid"},model:{value:t.formData.secret_key,callback:function(e){t.$set(t.formData,"secret_key",e)},expression:"formData.secret_key"}})],1),!t.checkIsFormStatus&&t.qrcodeValue?e("el-form-item",{attrs:{label:"地址",prop:""}},[e("el-input",{staticStyle:{width:"300px"},attrs:{readonly:""},model:{value:t.qrcodeValue,callback:function(e){t.qrcodeValue=e},expression:"qrcodeValue"}},[e("el-button",{directives:[{name:"clipboard",rawName:"v-clipboard:copy",value:t.qrcodeValue,expression:"qrcodeValue",arg:"copy"},{name:"clipboard",rawName:"v-clipboard:success",value:t.clipboardSuccess,expression:"clipboardSuccess",arg:"success"}],attrs:{slot:"append"},slot:"append"},[t._v("复制")])],1)],1):t._e(),!t.checkIsFormStatus&&t.qrcodeValue?e("el-form-item",{attrs:{label:"二维码",prop:""}},[e("qrcode",{staticClass:"face-img",attrs:{value:t.qrcodeValue,options:t.qrcodeOptions,tag:"img",margin:10,alt:""}})],1):t._e(),e("el-form-item",{attrs:{label:"功能菜单配置",prop:"menuList"}},[e("el-select",{staticClass:"ps-select w-300",attrs:{disabled:!t.checkIsFormStatus,multiple:!0},model:{value:t.formData.menuList,callback:function(e){t.$set(t.formData,"menuList",e)},expression:"formData.menuList"}},t._l(t.allMenuList,(function(t){return e("el-option",{key:t.key,attrs:{label:t.verbose_name,value:t.key}})})),1)],1),e("el-form-item",{attrs:{"label-width":"0",prop:"templateId"}},[e("span",{staticClass:"m-r-10"},[t._v("人脸更新提醒模板ID：")]),e("el-input",{staticStyle:{"max-width":"300px"},attrs:{disabled:!t.checkIsFormStatus,placeholder:"请输入模板id"},model:{value:t.formData.templateId,callback:function(e){t.$set(t.formData,"templateId",e)},expression:"formData.templateId"}})],1)],1),t.checkIsFormStatus?e("div",{staticClass:"add-wrapper"},[e("el-button",{directives:[{name:"permission",rawName:"v-permission",value:["background.admin.organization.modify"],expression:"['background.admin.organization.modify']"}],staticClass:"ps-origin-btn",attrs:{type:"primary",size:"small"},on:{click:t.saveAppidHandle}},[t._v("保存")])],1):t._e()],1)])}),[],!1,null,null,null);e.default=m.exports}}]);