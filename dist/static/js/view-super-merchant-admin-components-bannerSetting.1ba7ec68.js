(window.webpackJsonp=window.webpackJsonp||[]).push([["view-super-merchant-admin-components-bannerSetting"],{"0691":function(t,e,r){"use strict";r.r(e);var n=r("ed08");function a(t){return(a="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function o(){/*! regenerator-runtime -- Copyright (c) 2014-present, Facebook, Inc. -- license (MIT): https://github.com/facebook/regenerator/blob/main/LICENSE */o=function(){return e};var t,e={},r=Object.prototype,n=r.hasOwnProperty,i=Object.defineProperty||function(t,e,r){t[e]=r.value},s="function"==typeof Symbol?Symbol:{},c=s.iterator||"@@iterator",u=s.asyncIterator||"@@asyncIterator",f=s.toStringTag||"@@toStringTag";function l(t,e,r){return Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}),t[e]}try{l({},"")}catch(t){l=function(t,e,r){return t[e]=r}}function p(t,e,r,n){var a=e&&e.prototype instanceof y?e:y,o=Object.create(a.prototype),s=new $(n||[]);return i(o,"_invoke",{value:E(t,r,s)}),o}function h(t,e,r){try{return{type:"normal",arg:t.call(e,r)}}catch(t){return{type:"throw",arg:t}}}e.wrap=p;var d="suspendedStart",m="executing",g="completed",v={};function y(){}function b(){}function w(){}var L={};l(L,c,(function(){return this}));var x=Object.getPrototypeOf,k=x&&x(x(A([])));k&&k!==r&&n.call(k,c)&&(L=k);var O=w.prototype=y.prototype=Object.create(L);function S(t){["next","throw","return"].forEach((function(e){l(t,e,(function(t){return this._invoke(e,t)}))}))}function _(t,e){function r(o,i,s,c){var u=h(t[o],t,i);if("throw"!==u.type){var f=u.arg,l=f.value;return l&&"object"==a(l)&&n.call(l,"__await")?e.resolve(l.__await).then((function(t){r("next",t,s,c)}),(function(t){r("throw",t,s,c)})):e.resolve(l).then((function(t){f.value=t,s(f)}),(function(t){return r("throw",t,s,c)}))}c(u.arg)}var o;i(this,"_invoke",{value:function(t,n){function a(){return new e((function(e,a){r(t,n,e,a)}))}return o=o?o.then(a,a):a()}})}function E(e,r,n){var a=d;return function(o,i){if(a===m)throw Error("Generator is already running");if(a===g){if("throw"===o)throw i;return{value:t,done:!0}}for(n.method=o,n.arg=i;;){var s=n.delegate;if(s){var c=j(s,n);if(c){if(c===v)continue;return c}}if("next"===n.method)n.sent=n._sent=n.arg;else if("throw"===n.method){if(a===d)throw a=g,n.arg;n.dispatchException(n.arg)}else"return"===n.method&&n.abrupt("return",n.arg);a=m;var u=h(e,r,n);if("normal"===u.type){if(a=n.done?g:"suspendedYield",u.arg===v)continue;return{value:u.arg,done:n.done}}"throw"===u.type&&(a=g,n.method="throw",n.arg=u.arg)}}}function j(e,r){var n=r.method,a=e.iterator[n];if(a===t)return r.delegate=null,"throw"===n&&e.iterator.return&&(r.method="return",r.arg=t,j(e,r),"throw"===r.method)||"return"!==n&&(r.method="throw",r.arg=new TypeError("The iterator does not provide a '"+n+"' method")),v;var o=h(a,e.iterator,r.arg);if("throw"===o.type)return r.method="throw",r.arg=o.arg,r.delegate=null,v;var i=o.arg;return i?i.done?(r[e.resultName]=i.value,r.next=e.nextLoc,"return"!==r.method&&(r.method="next",r.arg=t),r.delegate=null,v):i:(r.method="throw",r.arg=new TypeError("iterator result is not an object"),r.delegate=null,v)}function B(t){var e={tryLoc:t[0]};1 in t&&(e.catchLoc=t[1]),2 in t&&(e.finallyLoc=t[2],e.afterLoc=t[3]),this.tryEntries.push(e)}function D(t){var e=t.completion||{};e.type="normal",delete e.arg,t.completion=e}function $(t){this.tryEntries=[{tryLoc:"root"}],t.forEach(B,this),this.reset(!0)}function A(e){if(e||""===e){var r=e[c];if(r)return r.call(e);if("function"==typeof e.next)return e;if(!isNaN(e.length)){var o=-1,i=function r(){for(;++o<e.length;)if(n.call(e,o))return r.value=e[o],r.done=!1,r;return r.value=t,r.done=!0,r};return i.next=i}}throw new TypeError(a(e)+" is not iterable")}return b.prototype=w,i(O,"constructor",{value:w,configurable:!0}),i(w,"constructor",{value:b,configurable:!0}),b.displayName=l(w,f,"GeneratorFunction"),e.isGeneratorFunction=function(t){var e="function"==typeof t&&t.constructor;return!!e&&(e===b||"GeneratorFunction"===(e.displayName||e.name))},e.mark=function(t){return Object.setPrototypeOf?Object.setPrototypeOf(t,w):(t.__proto__=w,l(t,f,"GeneratorFunction")),t.prototype=Object.create(O),t},e.awrap=function(t){return{__await:t}},S(_.prototype),l(_.prototype,u,(function(){return this})),e.AsyncIterator=_,e.async=function(t,r,n,a,o){void 0===o&&(o=Promise);var i=new _(p(t,r,n,a),o);return e.isGeneratorFunction(r)?i:i.next().then((function(t){return t.done?t.value:i.next()}))},S(O),l(O,f,"Generator"),l(O,c,(function(){return this})),l(O,"toString",(function(){return"[object Generator]"})),e.keys=function(t){var e=Object(t),r=[];for(var n in e)r.push(n);return r.reverse(),function t(){for(;r.length;){var n=r.pop();if(n in e)return t.value=n,t.done=!1,t}return t.done=!0,t}},e.values=A,$.prototype={constructor:$,reset:function(e){if(this.prev=0,this.next=0,this.sent=this._sent=t,this.done=!1,this.delegate=null,this.method="next",this.arg=t,this.tryEntries.forEach(D),!e)for(var r in this)"t"===r.charAt(0)&&n.call(this,r)&&!isNaN(+r.slice(1))&&(this[r]=t)},stop:function(){this.done=!0;var t=this.tryEntries[0].completion;if("throw"===t.type)throw t.arg;return this.rval},dispatchException:function(e){if(this.done)throw e;var r=this;function a(n,a){return s.type="throw",s.arg=e,r.next=n,a&&(r.method="next",r.arg=t),!!a}for(var o=this.tryEntries.length-1;o>=0;--o){var i=this.tryEntries[o],s=i.completion;if("root"===i.tryLoc)return a("end");if(i.tryLoc<=this.prev){var c=n.call(i,"catchLoc"),u=n.call(i,"finallyLoc");if(c&&u){if(this.prev<i.catchLoc)return a(i.catchLoc,!0);if(this.prev<i.finallyLoc)return a(i.finallyLoc)}else if(c){if(this.prev<i.catchLoc)return a(i.catchLoc,!0)}else{if(!u)throw Error("try statement without catch or finally");if(this.prev<i.finallyLoc)return a(i.finallyLoc)}}}},abrupt:function(t,e){for(var r=this.tryEntries.length-1;r>=0;--r){var a=this.tryEntries[r];if(a.tryLoc<=this.prev&&n.call(a,"finallyLoc")&&this.prev<a.finallyLoc){var o=a;break}}o&&("break"===t||"continue"===t)&&o.tryLoc<=e&&e<=o.finallyLoc&&(o=null);var i=o?o.completion:{};return i.type=t,i.arg=e,o?(this.method="next",this.next=o.finallyLoc,v):this.complete(i)},complete:function(t,e){if("throw"===t.type)throw t.arg;return"break"===t.type||"continue"===t.type?this.next=t.arg:"return"===t.type?(this.rval=this.arg=t.arg,this.method="return",this.next="end"):"normal"===t.type&&e&&(this.next=e),v},finish:function(t){for(var e=this.tryEntries.length-1;e>=0;--e){var r=this.tryEntries[e];if(r.finallyLoc===t)return this.complete(r.completion,r.afterLoc),D(r),v}},catch:function(t){for(var e=this.tryEntries.length-1;e>=0;--e){var r=this.tryEntries[e];if(r.tryLoc===t){var n=r.completion;if("throw"===n.type){var a=n.arg;D(r)}return a}}throw Error("illegal catch attempt")},delegateYield:function(e,r,n){return this.delegate={iterator:A(e),resultName:r,nextLoc:n},"next"===this.method&&(this.arg=t),v}},e}function i(t,e){return function(t){if(Array.isArray(t))return t}(t)||function(t,e){var r=null==t?null:"undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(null!=r){var n,a,o,i,s=[],c=!0,u=!1;try{if(o=(r=r.call(t)).next,0===e){if(Object(r)!==r)return;c=!1}else for(;!(c=(n=o.call(r)).done)&&(s.push(n.value),s.length!==e);c=!0);}catch(t){u=!0,a=t}finally{try{if(!c&&null!=r.return&&(i=r.return(),Object(i)!==i))return}finally{if(u)throw a}}return s}}(t,e)||function(t,e){if(t){if("string"==typeof t)return s(t,e);var r={}.toString.call(t).slice(8,-1);return"Object"===r&&t.constructor&&(r=t.constructor.name),"Map"===r||"Set"===r?Array.from(t):"Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r)?s(t,e):void 0}}(t,e)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function s(t,e){(null==e||e>t.length)&&(e=t.length);for(var r=0,n=Array(e);r<e;r++)n[r]=t[r];return n}function c(t,e,r,n,a,o,i){try{var s=t[o](i),c=s.value}catch(t){return void r(t)}s.done?e(c):Promise.resolve(c).then(n,a)}function u(t){return function(){var e=this,r=arguments;return new Promise((function(n,a){var o=t.apply(e,r);function i(t){c(o,n,a,i,s,"next",t)}function s(t){c(o,n,a,i,s,"throw",t)}i(void 0)}))}}var f={name:"SuperBannerSetting",props:{type:String,infoData:{type:Object,default:function(){return{}}},organizationData:Object,restoreHandle:Function},components:{},data:function(){return{formOperate:"detail",isLoading:!1,formData:{banner:[]},formDataRuls:{banner:[{required:!0,message:"请先选择",trigger:"blur"}]},bannerList:[]}},computed:{checkIsFormStatus:function(){var t=!1;switch(this.formOperate){case"detail":t=!1;break;case"modify":t=!0}return t}},watch:{},created:function(){},mounted:function(){this.initLoad()},methods:{initLoad:function(){this.getSelectBannerList()},refreshHandle:function(){this.initLoad()},searchHandle:Object(n.d)((function(){}),300),changeOperate:function(){"modify"!==this.formOperate?this.formOperate="modify":this.formOperate="detail"},getBannerList:function(){var t=this;return u(o().mark((function e(){var r,n,a,s,c;return o().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return t.isLoading=!0,r={org_id:t.organizationData.id,page:1,page_size:999999},e.next=4,t.$to(t.$apis.apiBackgroundAdminMarketingBannerListPost(r));case 4:if(n=e.sent,a=i(n,2),s=a[0],c=a[1],t.isLoading=!1,!s){e.next=12;break}return t.$message.error(s.message),e.abrupt("return");case 12:0===c.code&&(t.bannerList=c.data.results);case 13:case"end":return e.stop()}}),e)})))()},getSelectBannerList:function(){var t=this;return u(o().mark((function e(){var r,n,a,s,c;return o().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return t.isLoading=!0,r={org_id:t.organizationData.id,page:1,page_size:999999},e.next=4,t.$to(t.$apis.apiBackgroundAdminMarketingBannerGetOrgBannerListPost(r));case 4:if(n=e.sent,a=i(n,2),s=a[0],c=a[1],t.isLoading=!1,!s){e.next=12;break}return t.$message.error(s.message),e.abrupt("return");case 12:0===c.code?(t.bannerList=c.data,c.data.map((function(e){e.is_select&&t.formData.banner.push(e.id)}))):t.$message.error(c.msg);case 13:case"end":return e.stop()}}),e)})))()},saveSettingHandle:function(){var t=this;return u(o().mark((function e(){return o().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(!t.isLoading){e.next=2;break}return e.abrupt("return");case 2:t.$refs.appidRef.validate((function(e){e&&t.modifySetting()}));case 3:case"end":return e.stop()}}),e)})))()},modifySetting:function(){var t=this;return u(o().mark((function e(){var r,a,s,c,u;return o().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return r={org_id:t.organizationData.id,banner_ids:t.formData.banner},t.isLoading=!0,e.next=4,Object(n.X)(t.$apis.apiBackgroundAdminMarketingBannerSetOrgsPost(r));case 4:if(a=e.sent,s=i(a,2),c=s[0],u=s[1],t.isLoading=!1,!c){e.next=12;break}return t.$message.error(c.message),e.abrupt("return");case 12:0===u.code?(t.$message.success("修改成功"),t.formOperate="detail",t.restoreHandle(t.type,t.formOperate)):t.$message.error(u.msg);case 13:case"end":return e.stop()}}),e)})))()}}},l=(r("814e"),r("2877")),p=Object(l.a)(f,(function(){var t=this,e=t._self._c;return e("div",{directives:[{name:"loading",rawName:"v-loading",value:t.isLoading,expression:"isLoading"}],staticClass:"container-wrapper bannersetting"},[e("div",{staticClass:"l-title clearfix"},[e("span",{staticClass:"float-l min-title-h"},[t._v("首页轮播图")]),"detail"===t.formOperate?e("el-button",{directives:[{name:"permission",rawName:"v-permission",value:["background.admin.organization.modify"],expression:"['background.admin.organization.modify']"}],staticClass:"float-r",attrs:{size:"mini"},on:{click:t.changeOperate}},[t._v("编辑")]):t._e()],1),e("div",{staticClass:"appid-box"},[e("el-form",{ref:"appidRef",attrs:{rules:t.formDataRuls,model:t.formData,size:"small","label-width":"100px"}},[e("el-form-item",{attrs:{label:"首页轮播",prop:"banner"}},[e("el-select",{staticClass:"ps-select w-300",attrs:{disabled:!t.checkIsFormStatus,multiple:!0},model:{value:t.formData.banner,callback:function(e){t.$set(t.formData,"banner",e)},expression:"formData.banner"}},t._l(t.bannerList,(function(t){return e("el-option",{key:t.id,attrs:{label:t.name,value:t.id}})})),1)],1)],1),t.checkIsFormStatus?e("div",{staticClass:"add-wrapper"},[e("el-button",{staticClass:"ps-origin-btn",attrs:{type:"primary",size:"small"},on:{click:t.saveSettingHandle}},[t._v("保存")])],1):t._e()],1)])}),[],!1,null,null,null);e.default=p.exports},"814e":function(t,e,r){"use strict";r("8783")},8783:function(t,e,r){t.exports={menuText:"rgba(35,40,45,.7)",menuActiveText:"#23282d",subMenuActiveText:"#23282d",menuBg:"#fff",menuHover:"#edf1f5",subMenuBg:"#fff",subMenuHover:"rgba(0,0,0,.1)",sideBarWidth:"240px"}}}]);