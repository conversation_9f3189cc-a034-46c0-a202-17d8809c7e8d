(window.webpackJsonp=window.webpackJsonp||[]).push([["view-super-merchant-admin-components-addRootOrganization","view-merchant-consumption-rules-service-admin-AddChargeServiceRule","view-merchant-meal-management-components-mealFoodList-FoodDiscountDialog"],{"981c":function(e,t,a){e.exports={menuText:"rgba(35,40,45,.7)",menuActiveText:"#23282d",subMenuActiveText:"#23282d",menuBg:"#fff",menuHover:"#edf1f5",subMenuBg:"#fff",subMenuHover:"rgba(0,0,0,.1)",sideBarWidth:"240px"}},bcce:function(e,t,a){"use strict";a("981c")},c938:function(e){e.exports=JSON.parse('[{"id":"1","name":"互联网/电子商务"},{"id":"2","name":"IT软件与服务"},{"id":"3","name":"IT硬件与设备"},{"id":"4","name":"电子技术"},{"id":"5","name":"通信与运营商"},{"id":"6","name":"网络游戏"},{"id":"7","name":"银行"},{"id":"8","name":"基金|理财|信托"},{"id":"9","name":"保险"},{"id":"10","name":"餐饮"},{"id":"11","name":"酒店"},{"id":"12","name":"旅游"},{"id":"13","name":"快递"},{"id":"14","name":"物流"},{"id":"15","name":"仓储"},{"id":"16","name":"培训"},{"id":"17","name":"院校"},{"id":"18","name":"学术科研"},{"id":"19","name":"交警"},{"id":"20","name":"博物馆"},{"id":"21","name":"公共事业|非盈利机构"},{"id":"22","name":"医药医疗"},{"id":"23","name":"护理美容"},{"id":"24","name":"保健与卫生"},{"id":"25","name":"汽车相关"},{"id":"26","name":"摩托车相关"},{"id":"27","name":"火车相关"},{"id":"28","name":"飞机相关"},{"id":"29","name":"建筑"},{"id":"30","name":"物业"},{"id":"31","name":"消费品"},{"id":"32","name":"法律"},{"id":"33","name":"会展"},{"id":"34","name":"中介服务"},{"id":"35","name":"认证"},{"id":"36","name":"审计"},{"id":"37","name":"传媒"},{"id":"38","name":"体育"},{"id":"39","name":"娱乐休闲"},{"id":"40","name":"印刷"},{"id":"41","name":"其它"}]')},d0dd:function(e,t,a){"use strict";a.d(t,"a",(function(){return r})),a.d(t,"b",(function(){return i})),a.d(t,"g",(function(){return n})),a.d(t,"c",(function(){return o})),a.d(t,"f",(function(){return s})),a.d(t,"d",(function(){return l})),a.d(t,"e",(function(){return c}));var r=function(e,t,a){if(t){/^-?(([1-9][0-9]*)|(([0]\.\d{1,2}|[1-9][0-9]*\.\d{1,2})))$/.test(t)?a():a(new Error("金额格式有误"))}else a(new Error("请输入金额"))},i=function(e,t,a){if(t){/^-?(([0-9][0-9]*)|(([0]\.\d{1,2}|[1-9][0-9]*\.\d{1,2})))$/.test(t)?a():a(new Error("金额格式有误"))}else a()},n=function(e,t,a){if(!t)return a(new Error("手机号不能为空"));/^1[3456789]\d{9}$/.test(t)?a():a(new Error("请输入正确手机号"))},o=function(e,t,a){if(!t)return a(new Error("金额有误"));/^(([0]{1,1})|([1-9][0-9]*)|(([0]\.\d{1,2}|[1-9][0-9]*\.\d{1,2})))$/.test(t)?a():a(new Error("金额格式有误"))},s=function(e,t,a){if(""===t)return a(new Error("不能为空"));/^\d+$/.test(t)?a():a(new Error("请输入正确数字"))},l=function(e,t,a){if(""!==t){/^(\+|-)?(([0-9][0-9]*)|(([0]\.\d{1,2}|[1-9][0-9]*\.\d{1,2})))$/.test(t)?a():a(new Error("金额格式有误"))}else a(new Error("请输入金额"))},c=function(e,t,a){/^[\u4E00-\u9FA5\w-]+$/.test(t)?a():a(new Error("格式不正确，不能包含特殊字符"))}},e173:function(e,t,a){"use strict";a.d(t,"d",(function(){return i})),a.d(t,"h",(function(){return n})),a.d(t,"b",(function(){return o})),a.d(t,"a",(function(){return s})),a.d(t,"c",(function(){return l})),a.d(t,"g",(function(){return c})),a.d(t,"e",(function(){return m})),a.d(t,"f",(function(){return u}));var r=a("e925"),i=function(e,t,a){if(!t)return a();Object(r.a)(t)?a():a(new Error("邮箱格式错误！"))},n=function(e,t,a){if(!t)return a();Object(r.e)(t)?a():a(new Error("电话格式错误！"))},o=function(e,t,a){if(!t)return a();Object(r.g)(t)?a():a(new Error("长度5到20位，只支持数字、大小写英文或下划线组合"))},s=function(e,t,a){if(!t)return a();Object(r.c)(t)?a():a(new Error("密码长度8~20位，英文加数字"))},l=function(e,t,a){if(!t||"长期"===t)return a();if(Object(r.b)(t)){var i=t.toString().trim().replace(" ","");if(8!==i.length)return a();i=i.slice(0,4)+"/"+i.slice(4,6)+"/"+i.slice(6,i.length);var n=new Date(i).getTime();if(isNaN(n))return a(new Error("请输入正确的日期"));n<(new Date).getTime()&&a(new Error("有效期必须大于当前日期")),a()}a(new Error("请输入yyyyMMdd格式的日期"))},c=function(e,t,a){if(!t)return a();Object(r.f)(t)?a():a(new Error("电话/座机格式错误！"))},m=function(e,t,a){t?0===Number(t)?a(new Error("请输入大于0的数字！")):Object(r.h)(t)?a():a(new Error("最多2位数字可保留一位小数!")):a(new Error("请输入！"))},u=function(e,t,a){t?0===Number(t)?a(new Error("请输入大于0的数字！")):Object(r.j)(t)?a():a(new Error("最多1位数字可保留3位小数!")):a(new Error("请输入！"))}},e925:function(e,t,a){"use strict";a.d(t,"a",(function(){return r})),a.d(t,"e",(function(){return i})),a.d(t,"g",(function(){return n})),a.d(t,"c",(function(){return o})),a.d(t,"f",(function(){return s})),a.d(t,"d",(function(){return l})),a.d(t,"b",(function(){return c})),a.d(t,"i",(function(){return m})),a.d(t,"h",(function(){return u})),a.d(t,"j",(function(){return f}));var r=function(e){return/^[a-zA-Z0-9_-]+@[a-zA-Z0-9_-]+(\.[a-zA-Z0-9_-]+)+$/.test(e)},i=function(e){return/^([0-9]{3,4}-)?[0-9]{7,8}$/.test(e.toString())},n=function(e){return/^\w{5,20}$/.test(e)},o=function(e){return/^(?=.*[0-9])(?=.*[a-zA-Z])(.{8,20})$/.test(e)},s=function(e){return/(^([0-9]{3,4}-)?[0-9]{7,8}$)|(^1[3,4,5,6,7,8,9][0-9]{9}$)/.test(e.toString())},l=function(e){return/^-?(([0-9][0-9]*)|(([0]\.\d{1,2}|[1-9][0-9]*\.\d{1,2})))$/.test(e.toString())},c=function(e){return/\d/.test(e)},m=function(e){return/^(([0]{1,1})|([1-9][0-9]*)|(([0]\.\d{1,2}|[1-9][0-9]*\.\d{1,2})))$/.test(e)},u=function(e){return/^(([1-9]?[0-9])|([1-9]?[0-9]\.[1-9])|([1-9]?[1-9]\.[0]))$/.test(e)},f=function(e){return/^(([0-9])|([0-9]\.\d{1,3})|([1-9]\.[0]))$/.test(e)}},f850:function(e,t,a){"use strict";a.r(t);var r=a("ed08"),i=a("ef6c"),n=a("c938"),o=a("d0dd"),s=a("e173");function l(e){return(l="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}function c(e,t){return function(e){if(Array.isArray(e))return e}(e)||function(e,t){var a=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null!=a){var r,i,n,o,s=[],l=!0,c=!1;try{if(n=(a=a.call(e)).next,0===t){if(Object(a)!==a)return;l=!1}else for(;!(l=(r=n.call(a)).done)&&(s.push(r.value),s.length!==t);l=!0);}catch(e){c=!0,i=e}finally{try{if(!l&&null!=a.return&&(o=a.return(),Object(o)!==o))return}finally{if(c)throw i}}return s}}(e,t)||function(e,t){if(e){if("string"==typeof e)return m(e,t);var a={}.toString.call(e).slice(8,-1);return"Object"===a&&e.constructor&&(a=e.constructor.name),"Map"===a||"Set"===a?Array.from(e):"Arguments"===a||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(a)?m(e,t):void 0}}(e,t)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function m(e,t){(null==t||t>e.length)&&(t=e.length);for(var a=0,r=Array(t);a<t;a++)r[a]=e[a];return r}function u(){/*! regenerator-runtime -- Copyright (c) 2014-present, Facebook, Inc. -- license (MIT): https://github.com/facebook/regenerator/blob/main/LICENSE */u=function(){return t};var e,t={},a=Object.prototype,r=a.hasOwnProperty,i=Object.defineProperty||function(e,t,a){e[t]=a.value},n="function"==typeof Symbol?Symbol:{},o=n.iterator||"@@iterator",s=n.asyncIterator||"@@asyncIterator",c=n.toStringTag||"@@toStringTag";function m(e,t,a){return Object.defineProperty(e,t,{value:a,enumerable:!0,configurable:!0,writable:!0}),e[t]}try{m({},"")}catch(e){m=function(e,t,a){return e[t]=a}}function f(e,t,a,r){var n=t&&t.prototype instanceof g?t:g,o=Object.create(n.prototype),s=new L(r||[]);return i(o,"_invoke",{value:S(e,a,s)}),o}function d(e,t,a){try{return{type:"normal",arg:e.call(t,a)}}catch(e){return{type:"throw",arg:e}}}t.wrap=f;var p="suspendedStart",h="executing",b="completed",v={};function g(){}function y(){}function k(){}var D={};m(D,o,(function(){return this}));var x=Object.getPrototypeOf,w=x&&x(x(A([])));w&&w!==a&&r.call(w,o)&&(D=w);var C=k.prototype=g.prototype=Object.create(D);function _(e){["next","throw","return"].forEach((function(t){m(e,t,(function(e){return this._invoke(t,e)}))}))}function O(e,t){function a(i,n,o,s){var c=d(e[i],e,n);if("throw"!==c.type){var m=c.arg,u=m.value;return u&&"object"==l(u)&&r.call(u,"__await")?t.resolve(u.__await).then((function(e){a("next",e,o,s)}),(function(e){a("throw",e,o,s)})):t.resolve(u).then((function(e){m.value=e,o(m)}),(function(e){return a("throw",e,o,s)}))}s(c.arg)}var n;i(this,"_invoke",{value:function(e,r){function i(){return new t((function(t,i){a(e,r,t,i)}))}return n=n?n.then(i,i):i()}})}function S(t,a,r){var i=p;return function(n,o){if(i===h)throw Error("Generator is already running");if(i===b){if("throw"===n)throw o;return{value:e,done:!0}}for(r.method=n,r.arg=o;;){var s=r.delegate;if(s){var l=$(s,r);if(l){if(l===v)continue;return l}}if("next"===r.method)r.sent=r._sent=r.arg;else if("throw"===r.method){if(i===p)throw i=b,r.arg;r.dispatchException(r.arg)}else"return"===r.method&&r.abrupt("return",r.arg);i=h;var c=d(t,a,r);if("normal"===c.type){if(i=r.done?b:"suspendedYield",c.arg===v)continue;return{value:c.arg,done:r.done}}"throw"===c.type&&(i=b,r.method="throw",r.arg=c.arg)}}}function $(t,a){var r=a.method,i=t.iterator[r];if(i===e)return a.delegate=null,"throw"===r&&t.iterator.return&&(a.method="return",a.arg=e,$(t,a),"throw"===a.method)||"return"!==r&&(a.method="throw",a.arg=new TypeError("The iterator does not provide a '"+r+"' method")),v;var n=d(i,t.iterator,a.arg);if("throw"===n.type)return a.method="throw",a.arg=n.arg,a.delegate=null,v;var o=n.arg;return o?o.done?(a[t.resultName]=o.value,a.next=t.nextLoc,"return"!==a.method&&(a.method="next",a.arg=e),a.delegate=null,v):o:(a.method="throw",a.arg=new TypeError("iterator result is not an object"),a.delegate=null,v)}function T(e){var t={tryLoc:e[0]};1 in e&&(t.catchLoc=e[1]),2 in e&&(t.finallyLoc=e[2],t.afterLoc=e[3]),this.tryEntries.push(t)}function I(e){var t=e.completion||{};t.type="normal",delete t.arg,e.completion=t}function L(e){this.tryEntries=[{tryLoc:"root"}],e.forEach(T,this),this.reset(!0)}function A(t){if(t||""===t){var a=t[o];if(a)return a.call(t);if("function"==typeof t.next)return t;if(!isNaN(t.length)){var i=-1,n=function a(){for(;++i<t.length;)if(r.call(t,i))return a.value=t[i],a.done=!1,a;return a.value=e,a.done=!0,a};return n.next=n}}throw new TypeError(l(t)+" is not iterable")}return y.prototype=k,i(C,"constructor",{value:k,configurable:!0}),i(k,"constructor",{value:y,configurable:!0}),y.displayName=m(k,c,"GeneratorFunction"),t.isGeneratorFunction=function(e){var t="function"==typeof e&&e.constructor;return!!t&&(t===y||"GeneratorFunction"===(t.displayName||t.name))},t.mark=function(e){return Object.setPrototypeOf?Object.setPrototypeOf(e,k):(e.__proto__=k,m(e,c,"GeneratorFunction")),e.prototype=Object.create(C),e},t.awrap=function(e){return{__await:e}},_(O.prototype),m(O.prototype,s,(function(){return this})),t.AsyncIterator=O,t.async=function(e,a,r,i,n){void 0===n&&(n=Promise);var o=new O(f(e,a,r,i),n);return t.isGeneratorFunction(a)?o:o.next().then((function(e){return e.done?e.value:o.next()}))},_(C),m(C,c,"Generator"),m(C,o,(function(){return this})),m(C,"toString",(function(){return"[object Generator]"})),t.keys=function(e){var t=Object(e),a=[];for(var r in t)a.push(r);return a.reverse(),function e(){for(;a.length;){var r=a.pop();if(r in t)return e.value=r,e.done=!1,e}return e.done=!0,e}},t.values=A,L.prototype={constructor:L,reset:function(t){if(this.prev=0,this.next=0,this.sent=this._sent=e,this.done=!1,this.delegate=null,this.method="next",this.arg=e,this.tryEntries.forEach(I),!t)for(var a in this)"t"===a.charAt(0)&&r.call(this,a)&&!isNaN(+a.slice(1))&&(this[a]=e)},stop:function(){this.done=!0;var e=this.tryEntries[0].completion;if("throw"===e.type)throw e.arg;return this.rval},dispatchException:function(t){if(this.done)throw t;var a=this;function i(r,i){return s.type="throw",s.arg=t,a.next=r,i&&(a.method="next",a.arg=e),!!i}for(var n=this.tryEntries.length-1;n>=0;--n){var o=this.tryEntries[n],s=o.completion;if("root"===o.tryLoc)return i("end");if(o.tryLoc<=this.prev){var l=r.call(o,"catchLoc"),c=r.call(o,"finallyLoc");if(l&&c){if(this.prev<o.catchLoc)return i(o.catchLoc,!0);if(this.prev<o.finallyLoc)return i(o.finallyLoc)}else if(l){if(this.prev<o.catchLoc)return i(o.catchLoc,!0)}else{if(!c)throw Error("try statement without catch or finally");if(this.prev<o.finallyLoc)return i(o.finallyLoc)}}}},abrupt:function(e,t){for(var a=this.tryEntries.length-1;a>=0;--a){var i=this.tryEntries[a];if(i.tryLoc<=this.prev&&r.call(i,"finallyLoc")&&this.prev<i.finallyLoc){var n=i;break}}n&&("break"===e||"continue"===e)&&n.tryLoc<=t&&t<=n.finallyLoc&&(n=null);var o=n?n.completion:{};return o.type=e,o.arg=t,n?(this.method="next",this.next=n.finallyLoc,v):this.complete(o)},complete:function(e,t){if("throw"===e.type)throw e.arg;return"break"===e.type||"continue"===e.type?this.next=e.arg:"return"===e.type?(this.rval=this.arg=e.arg,this.method="return",this.next="end"):"normal"===e.type&&t&&(this.next=t),v},finish:function(e){for(var t=this.tryEntries.length-1;t>=0;--t){var a=this.tryEntries[t];if(a.finallyLoc===e)return this.complete(a.completion,a.afterLoc),I(a),v}},catch:function(e){for(var t=this.tryEntries.length-1;t>=0;--t){var a=this.tryEntries[t];if(a.tryLoc===e){var r=a.completion;if("throw"===r.type){var i=r.arg;I(a)}return i}}throw Error("illegal catch attempt")},delegateYield:function(t,a,r){return this.delegate={iterator:A(t),resultName:a,nextLoc:r},"next"===this.method&&(this.arg=e),v}},t}function f(e,t,a,r,i,n,o){try{var s=e[n](o),l=s.value}catch(e){return void a(e)}s.done?t(l):Promise.resolve(l).then(r,i)}function d(e){return function(){var t=this,a=arguments;return new Promise((function(r,i){var n=e.apply(t,a);function o(e){f(n,r,i,o,s,"next",e)}function s(e){f(n,r,i,o,s,"throw",e)}o(void 0)}))}}var p={name:"SuperAddRootOrganization",components:{SelectTree:a("fb36").a},props:{type:String,infoData:{type:Object,default:function(){return{}}},treeData:Object,id:[String,Number],operate:String,restoreHandle:Function},data:function(){var e=this,t=function(t,a,r){if(!a)return"modify"===e.formOperate?void r():r(new Error("密码不能为空"));/^(?=.*[0-9])(?=.*[a-zA-Z])(.{8,20})$/.test(a)?r():r(new Error("密码长度8到20位，字母和数组组合"))};return{labelName:"",formOperate:"detail",isLoading:!1,industryTypeList:n,addrOptions:i.regionData,formData:{id:"",appid:"",secretKey:"",name:"",levelName:"",initOrganizationLevel:"",permission:[],merchantAppPermission:[],username:"",password:"",url:"",district:[],contact:"",mobile:"",mailAddress:"",tel:"",industry:"",remark:"",facepay:!1,refundOn:!1,refundPassword:"",storeWalletOn:!1,electronicWalletOn:!1,subsidyWalletOn:!1,complimentaryWalletOn:!1,otherWalletOn:!1,isThirdInterface:!1,combineWalletOn:!1,thirdAppKey:"",thirdSecretKey:"",thirdAppName:"",thirdAppUrl:"",thirdAppCallbackUrl:"",smsTemplateId:"",isAbcProject:!1,isExpireChangePwd:!1,allowJumpChangePwd:!1,useCardNoLimit:!1,enableUpdateNotify:!1,faceUpdateTime:"",notifyMsg:"",isWalletPayOrderAsc:!1,channel_id:[],is_member_on:!1},formDataRuls:{name:[{required:!0,message:"组织名称不能为空",trigger:"blur"},{validator:o.e,trigger:"blur"}],level_name:[{required:!0,message:"层级名称不能为空",trigger:"blur"}],mobile:[{required:!0,validator:o.g,trigger:"blur"}],username:[{required:!0,validator:function(e,t,a){if(!t)return a(new Error("账号不能为空"));/^\w{5,20}$/.test(t)?a():a(new Error("账号长度5到20位，只支持数字、大小写英文或下划线组合"))},trigger:"blur"}],password:[{required:!0,validator:t,trigger:"blur"}],refundPassword:[{validator:t,trigger:"blur"}],tel:[{validator:s.h,trigger:"blur"}],mailAddress:[{validator:s.d,trigger:"blur"}],permission:[{required:!0,validator:function(e,t,a){t.length>0?a():a(new Error("功能菜单配置不能为空！"))},trigger:"blur"}],useCardNoLimit:[{required:!0,message:"不能为空",trigger:"blur"}],faceUpdateTime:[{required:!0,message:"请选择人脸更新天数",trigger:"blur"}],customFaceDate:[{validator:function(e,t,a){if(""===t||"0"===t)return a(new Error("请输入大于0的数字"));/^\d+$/.test(t)?a():a(new Error("请输入正确数字"))},trigger:"blur"}]},levelList:[],mobileTree:[{id:"folder",label:"Normal Folder",children:[{id:"disabled-checked",label:"Checked",isDisabled:!0},{id:"disabled-unchecked",label:"Unchecked",isDisabled:!0},{id:"item-1",label:"Item"}]}],permissionTree:[{id:"folder",label:"Normal Folder",children:[{id:"disabled-checked",label:"Checked",isDisabled:!0},{id:"disabled-unchecked",label:"Unchecked",isDisabled:!0},{id:"item-1",label:"Item"}]}],permissionTreeProps:{value:"key",label:"verbose_name",isLeaf:"is_leaf",children:"children"},loadingThirdInfo:!1,faceUploadOptions:[{name:"60天",value:60},{name:"90天",value:90},{name:"180天",value:180},{name:"1年",value:365},{name:"自定义",value:"auto"}],cascaderProps:{label:"name",value:"id",children:"children_list",checkStrictly:!0},channelTreeList:[]}},computed:{checkIsFormStatus:function(){var e=!1;switch(this.operate){case"add":e=!0;break;case"detail":default:e="detail"!==this.formOperate}return e}},watch:{operate:function(e,t){e||(this.formOperate="detail")}},created:function(){},mounted:function(){this.initLoad()},methods:{initLoad:function(){var e=this;return d(u().mark((function t(){return u().wrap((function(t){for(;;)switch(t.prev=t.next){case 0:return t.next=2,e.initDic();case 2:e.getLevelList(e.id),e.getPermissionTreeList(e.id),e.getMobileList(e.id),e.id&&"add"!==e.operate&&e.initInfoHandle(),e.operate&&(e.formOperate=e.operate),e.treeData&&"add"!==e.operate&&(e.labelName=e.treeData.name.substring(0,1)),"add"===e.operate&&(e.labelName="朴");case 9:case"end":return t.stop()}}),t)})))()},refreshHandle:function(){this.initLoad()},searchHandle:Object(r.d)((function(){}),300),initInfoHandle:function(){var e,t=this,a=function(){var a=t.infoData[Object(r.b)(i)];if(a)switch(i){case"industry":t.formData[i]=a.toString();break;case"district":t.formData[i]=JSON.parse(a);break;case"channel_id":null!==a&&(e=t.getParentsById(t.channelTreeList,a),t.formData[i]=e);break;case"faceUpdateTime":var n=!1;t.faceUploadOptions.forEach((function(e){e.value==a&&(n=!0)})),n?t.formData[i]=a:a&&(t.formData[i]="auto",t.formData.customFaceDate=a);break;default:t.formData[i]=a}};for(var i in this.formData)a()},permissionNormalizer:function(e){return{id:e.key,label:e.verbose_name,children:e.children}},mobileNormalizer:function(e){return{id:e.key,label:e.verbose_name,children:e.children}},deleteEmptyChildren:function(e,t){t=t||"children_list";var a=this;return function e(r){r.map((function(r){a.checkIsFormStatus?r.isDisabled=!1:r.isDisabled=!0,r[t]&&r[t].length>0?e(r[t]):a.$delete(r,t)}))}(e),e},getLevelList:function(e){var t=this;return d(u().mark((function a(){var i,n,o,s,l;return u().wrap((function(a){for(;;)switch(a.prev=a.next){case 0:return i={},e&&(i.company_id=e),a.next=4,Object(r.X)(t.$apis.apiBackgroundAdminOrganizationGetLevelNameMapPost(i));case 4:if(n=a.sent,o=c(n,2),s=o[0],l=o[1],!s){a.next=11;break}return t.$message.error(s.message),a.abrupt("return");case 11:0===l.code?(t.levelList=l.data,l.data.length>0&&"add"===t.operate&&(t.formData.levelName=l.data[0].name)):t.$message.error(l.msg);case 12:case"end":return a.stop()}}),a)})))()},getPermissionTreeList:function(){var e=this;return d(u().mark((function t(){var a,i,n,o;return u().wrap((function(t){for(;;)switch(t.prev=t.next){case 0:return t.next=2,Object(r.X)(e.$apis.apiBackgroundAdminOrganizationGetMerchantPermissionsPost());case 2:if(a=t.sent,i=c(a,2),n=i[0],o=i[1],!n){t.next=9;break}return e.$message.error(n.message),t.abrupt("return");case 9:0===o.code?(e.permissionTree=e.deleteEmptyChildren(o.data,"children"),"add"===e.operate&&(e.formData.permission=Object(r.D)(e.permissionTree,"key"))):e.$message.error(o.msg);case 10:case"end":return t.stop()}}),t)})))()},getMobileList:function(){var e=this;return d(u().mark((function t(){var a,i,n,o;return u().wrap((function(t){for(;;)switch(t.prev=t.next){case 0:return t.next=2,Object(r.X)(e.$apis.apiBackgroundAdminOrganizationGetMerchantMobilePermissions());case 2:if(a=t.sent,i=c(a,2),n=i[0],o=i[1],!n){t.next=9;break}return e.$message.error(n.message),t.abrupt("return");case 9:0===o.code?(e.mobileTree=e.deleteEmptyChildren(o.data,"children"),"add"===e.operate&&(e.formData.merchantAppPermission=Object(r.D)(e.mobileTree,"key"))):e.$message.error(o.msg);case 10:case"end":return t.stop()}}),t)})))()},clickSelectPermissionTree:function(e){this.formData.permission=1===e?Object(r.D)(this.permissionTree,"key"):[]},clickSelectMobileTree:function(e){this.formData.merchantAppPermission=1===e?Object(r.D)(this.mobileTree,"key"):null},changeOperate:function(){switch(this.operate){case"add":break;default:"detail"===this.formOperate?this.formOperate="modify":this.formOperate="detail"}this.permissionTree=this.deleteEmptyChildren(this.permissionTree,"children"),this.mobileTree=this.deleteEmptyChildren(this.mobileTree,"children")},cancelFormHandle:function(){"add"===this.operate?this.$refs.organizationFormRef.resetFields():(this.$refs.organizationFormRef.clearValidate(),this.formOperate="detail",this.permissionTree=this.deleteEmptyChildren(this.permissionTree,"children"),this.mobileTree=this.deleteEmptyChildren(this.mobileTree,"children")),this.restoreHandle(this.type,this.formOperate)},generateThirdAppinfo:function(){var e=this;return d(u().mark((function t(){var a,i,n,o;return u().wrap((function(t){for(;;)switch(t.prev=t.next){case 0:return e.loadingThirdInfo=!0,t.next=3,Object(r.X)(e.$apis.apiBackgroundAdminOrganizationGenerateThirdAppinfoPost({id:e.id}));case 3:if(a=t.sent,i=c(a,2),n=i[0],o=i[1],e.loadingThirdInfo=!1,!n){t.next=11;break}return e.$message.error(n.message),t.abrupt("return");case 11:0===o.code?(e.formData.thirdAppKey=o.data.third_app_key,e.formData.thirdSecretKey=o.data.third_secret_key):e.$message.error(o.msg);case 12:case"end":return t.stop()}}),t)})))()},sendFormdataHandle:function(){var e=this;this.$refs.organizationFormRef.validate((function(t){t&&("add"===e.operate?e.addRootOrganization(e.formatData()):e.modifyOrganization(e.formatData()))}))},formatData:function(){var e={status:"enable"};for(var t in this.formData){var a=this.formData[t];if(""!==a){switch(t){case"district":a=JSON.stringify(a);break;case"channel_id":if(this.formData.channel_id&&0!==this.formData.channel_id.length){var i=null;if(this.$refs.channelMul){var n=this.$refs.channelMul.getCheckedNodes({leafOnly:!1});n&&n.length>0&&(i=n[0].value)}a=i}else a=null;break;case"password":case"refundPassword":break;case"thirdAppUrl":a=encodeURIComponent(a);break;case"faceUpdateTime":a="auto"===a?this.formData.customFaceDate:a}"levelName"!==t&&"customFaceDate"!==t&&(e[Object(r.b)(t)]=a)}"modify"===this.formOperate&&(e.company=this.treeData.company)}return null!==e.channel_id&&0!==e.channel_id.length||delete e.channel_id,e},getPermissionLevelParent:function(e){var t=this,a=[];function r(e,t,a){for(var i=[],n=0;n<e.length;n++){var o=e[n];if(o.key===t){i=a;break}a.push(t),o.children&&o.children.length>0&&r(o.children,t,a)}return i}return e.forEach((function(e){var i=[],n=r(t.permissionTree,e,i),o=r(t.mobileTree,e,i);a=(a=a.concat(n)).concat(o)})),a},addRootOrganization:function(e){var t=this;return d(u().mark((function a(){var i,n,o,s;return u().wrap((function(a){for(;;)switch(a.prev=a.next){case 0:return t.isLoading=!0,a.next=3,Object(r.X)(t.$apis.apiBackgroundAdminOrganizationAddRootPost(e));case 3:if(i=a.sent,n=c(i,2),o=n[0],s=n[1],t.isLoading=!1,!o){a.next=11;break}return t.$message.error(o.message),a.abrupt("return");case 11:0===s.code?(t.$message.success("添加成功"),t.restoreHandle(t.type,t.formOperate)):t.$message.error(s.msg);case 12:case"end":return a.stop()}}),a)})))()},modifyOrganization:function(e){var t=this;return d(u().mark((function a(){var i,n,o,s;return u().wrap((function(a){for(;;)switch(a.prev=a.next){case 0:return t.isLoading=!0,a.next=3,Object(r.X)(t.$apis.apiBackgroundAdminOrganizationModifyPost(e));case 3:if(i=a.sent,n=c(i,2),o=n[0],s=n[1],t.isLoading=!1,!o){a.next=11;break}return t.$message.error(o.message),a.abrupt("return");case 11:0===s.code?(t.$message.success("修改成功"),t.formOperate="detail",t.restoreHandle(t.type,t.formOperate)):t.$message.error(s.msg);case 12:case"end":return a.stop()}}),a)})))()},gotoLogin:function(){if(this.infoData.login_token){var e=document.createElement("a");e.href=location.origin+"/#/login?token="+this.infoData.login_token,e.target="_blank",e.click(),e=null}else this.$message.error("无法获取token!")},initDic:function(){var e=this;return d(u().mark((function t(){var a;return u().wrap((function(t){for(;;)switch(t.prev=t.next){case 0:return t.next=2,e.getChannelTreeList();case 2:a=t.sent,Array.isArray(a)&&a.length>0&&(e.channelTreeList=Object(r.f)(a));case 5:case"end":return t.stop()}}),t)})))()},getChannelTreeList:function(){var e=this;return new Promise((function(t){e.$apis.apiBackgroundAdminChannelTreeListPost().then((function(e){if(Reflect.has(e,"code")&&0===e.code){var a=e.data||{};Object(r.R)(a.results,"children_list"),t(a.results)}t([])})).catch((function(e){t([])}))}))},getParentsById:function(e,t){for(var a in e){if(e[a].id===t)return[e[a].id];if(e[a].children_list){var r=this.getParentsById(e[a].children_list,t);if(void 0!==r)return r.unshift(e[a].id),r}}}}},h=(a("bcce"),a("2877")),b=Object(h.a)(p,(function(){var e=this,t=e._self._c;return t("div",{staticClass:"container-wrapper super-add-organization is-fixed-footer"},[t("el-form",{directives:[{name:"loading",rawName:"v-loading",value:e.isLoading,expression:"isLoading"}],ref:"organizationFormRef",staticClass:"organization-form-wrapper",attrs:{rules:e.formDataRuls,model:e.formData,size:"small"}},["add"===e.operate?t("div",{staticClass:"add-title"},[e._v("新建组织")]):e._e(),t("div",{staticClass:"l-title clearfix"},[t("span",{staticClass:"float-l min-title-h"},[e._v("基本信息")]),e.checkIsFormStatus?e._e():t("el-button",{directives:[{name:"permission",rawName:"v-permission",value:["background.admin.organization.modify"],expression:"['background.admin.organization.modify']"}],staticClass:"float-r",attrs:{size:"mini"},on:{click:e.changeOperate}},[e._v(" 编辑 ")])],1),t("div",{staticClass:"item-box clearfix"},[t("div",{staticClass:"item-b-l"},[e._v(e._s(e.labelName))]),t("div",{staticClass:"item-b-r"},[t("el-form-item",{staticClass:"block-label",attrs:{label:"组织名称：",prop:"name"}},[e.checkIsFormStatus?t("el-input",{staticClass:"ps-input",attrs:{size:"small"},model:{value:e.formData.name,callback:function(t){e.$set(e.formData,"name",t)},expression:"formData.name"}}):t("div",{staticClass:"item-form-text"},[e._v(e._s(e.formData.name))])],1)],1)]),t("el-form-item",{staticClass:"block-label form-item-box",attrs:{label:"当前组织层次：",prop:"levelName"}},[t("div",{staticClass:"item-form-text"},[e._v(e._s(e.formData.levelName))])]),t("el-form-item",{staticClass:"block-label form-item-box",attrs:{label:"官网：",prop:"url"}},[e.checkIsFormStatus?t("el-input",{staticClass:"ps-input",attrs:{size:"small"},model:{value:e.formData.url,callback:function(t){e.$set(e.formData,"url",t)},expression:"formData.url"}}):t("div",{staticClass:"item-form-text"},[e._v(e._s(e.formData.url))])],1),t("el-row",{staticClass:"form-item-row-box",attrs:{gutter:24}},[t("el-col",{attrs:{span:12}},[t("el-form-item",{staticClass:"block-label form-item-box",attrs:{label:"固定电话：",prop:"tel"}},[e.checkIsFormStatus?t("el-input",{staticClass:"ps-input",attrs:{size:"small"},model:{value:e.formData.tel,callback:function(t){e.$set(e.formData,"tel",t)},expression:"formData.tel"}}):t("div",{staticClass:"item-form-text"},[e._v(e._s(e.formData.tel))])],1)],1),t("el-col",{attrs:{span:12}},[t("div",{staticClass:"form-item-box"},[t("el-form-item",{staticClass:"block-label",attrs:{label:"组织邮箱：",prop:"mailAddress"}},[e.checkIsFormStatus?t("el-input",{staticClass:"ps-input",attrs:{size:"small"},model:{value:e.formData.mailAddress,callback:function(t){e.$set(e.formData,"mailAddress",t)},expression:"formData.mailAddress"}}):t("div",{staticClass:"item-form-text"},[e._v(e._s(e.formData.mailAddress))])],1)],1)])],1),t("el-row",{staticClass:"form-item-row-box",attrs:{gutter:24}},[t("el-col",{staticClass:"block-label form-item-box",attrs:{span:12}},[t("el-form-item",{attrs:{label:"行业性质：",prop:"industry"}},[t("el-select",{staticClass:"ps-select",staticStyle:{width:"100%"},attrs:{placeholder:"请选择行业性质",size:"small",disabled:!e.checkIsFormStatus},model:{value:e.formData.industry,callback:function(t){e.$set(e.formData,"industry",t)},expression:"formData.industry"}},e._l(e.industryTypeList,(function(e){return t("el-option",{key:e.id,attrs:{label:e.name,value:e.id}})})),1)],1)],1),t("el-col",{staticClass:"block-label form-item-box",attrs:{span:12}},[t("el-form-item",{attrs:{label:"所在地址：",prop:"district"}},[t("el-cascader",{staticStyle:{display:"block"},attrs:{size:"small",options:e.addrOptions,disabled:!e.checkIsFormStatus,filterable:""},model:{value:e.formData.district,callback:function(t){e.$set(e.formData,"district",t)},expression:"formData.district"}})],1)],1),t("el-col",{staticClass:"block-label form-item-box",attrs:{span:12}},[t("el-form-item",{staticClass:"block-label",attrs:{label:"所属渠道",prop:"channel_id"}},[t("el-cascader",{ref:"channelMul",staticClass:"ps-select",staticStyle:{width:"80%"},attrs:{placeholder:"请选择",clearable:"",options:e.channelTreeList,"show-all-levels":!1,props:e.cascaderProps,disabled:!e.checkIsFormStatus,filterable:""},model:{value:e.formData.channel_id,callback:function(t){e.$set(e.formData,"channel_id",t)},expression:"formData.channel_id"}})],1)],1)],1),t("div",{staticClass:"form-line"}),t("div",{staticClass:"l-title clearfix"},[t("span",{staticClass:"float-l min-title-h"},[e._v("功能配置")])]),"add"===e.operate?t("el-form-item",{staticClass:"block-label form-item-box",attrs:{label:"添加组织层级：",prop:"initOrganizationLevel"}},[t("el-select",{staticStyle:{width:"100%"},attrs:{clearable:"",placeholder:"请选择"},model:{value:e.formData.initOrganizationLevel,callback:function(t){e.$set(e.formData,"initOrganizationLevel",t)},expression:"formData.initOrganizationLevel"}},e._l(e.levelList,(function(e){return t("el-option",{key:e.level,attrs:{label:e.name,value:e.level}})})),1)],1):e._e(),t("el-form-item",{staticClass:"block-label form-item-box",attrs:{label:"功能菜单配置：",prop:"permission"}},[e.checkIsFormStatus?t("div",{staticStyle:{margin:"3px 0 5px"}},[t("el-button",{attrs:{type:"",size:"mini"},on:{click:function(t){return e.clickSelectPermissionTree(1)}}},[e._v("全选")]),t("el-button",{attrs:{type:"",size:"mini"},on:{click:function(t){return e.clickSelectPermissionTree(0)}}},[e._v("全不选")])],1):e._e(),t("select-tree",{staticClass:"search-item-w ps-input",attrs:{placeholder:"请选择适用的下级组织",multiple:!0,"check-strictly":!0,"append-to-body":!0,"is-select-child":!0,"default-expand-all":!0,treeData:e.permissionTree,treeProps:e.permissionTreeProps,clearable:!0,disabled:!e.checkIsFormStatus},model:{value:e.formData.permission,callback:function(t){e.$set(e.formData,"permission",t)},expression:"formData.permission"}})],1),t("el-form-item",{staticClass:"block-label form-item-box",attrs:{label:"移动端菜单配置：",prop:"mobile"}},[e.checkIsFormStatus?t("div",{staticStyle:{margin:"3px 0 5px"}},[t("el-button",{attrs:{type:"",size:"mini"},on:{click:function(t){return e.clickSelectMobileTree(1)}}},[e._v("全选")]),t("el-button",{attrs:{type:"",size:"mini"},on:{click:function(t){return e.clickSelectMobileTree(0)}}},[e._v("全不选")])],1):e._e(),t("tree-select",{attrs:{multiple:!0,options:e.mobileTree,limit:5,limitText:function(e){return"+"+e},"default-expand-level":6,normalizer:e.mobileNormalizer,placeholder:"请选择","value-consists-of":"ALL","no-results-text":"暂无数据",flat:!0},model:{value:e.formData.merchantAppPermission,callback:function(t){e.$set(e.formData,"merchantAppPermission",t)},expression:"formData.merchantAppPermission"}})],1),t("el-form-item",{staticClass:"block-label form-item-box",attrs:{label:"",prop:"useCardNoLimit"}},[t("span",[t("span",{staticClass:"warn"},[e._v("*")]),e._v(" IC卡验证 "),t("el-radio-group",{staticClass:"ps-radio",attrs:{disabled:!e.checkIsFormStatus},model:{value:e.formData.useCardNoLimit,callback:function(t){e.$set(e.formData,"useCardNoLimit",t)},expression:"formData.useCardNoLimit"}},[t("el-radio",{attrs:{label:!0}},[e._v("是")]),t("el-radio",{attrs:{label:!1}},[e._v("否")])],1)],1)]),"root"===e.type?t("el-form-item",{staticClass:"block-label form-item-box fixed-login-box",attrs:{label:"账号：",prop:"username"}},["root"===e.type&&"add"!==e.operate?t("span",{staticClass:"fixed-login"},[t("el-button",{attrs:{type:"text",size:"mini"},on:{click:e.gotoLogin}},[e._v("登录")])],1):e._e(),"root"===e.type&&"add"===e.operate?t("el-input",{staticClass:"ps-input",attrs:{size:"small"},model:{value:e.formData.username,callback:function(t){e.$set(e.formData,"username",t)},expression:"formData.username"}}):t("div",{staticClass:"item-form-text"},[e._v(e._s(e.formData.username))])],1):e._e(),"root"===e.type&&"add"===e.operate?t("el-form-item",{staticClass:"block-label form-item-box",attrs:{label:"密码：",prop:"password"}},[t("el-input",{staticClass:"ps-input",attrs:{disabled:!e.checkIsFormStatus,size:"small"},model:{value:e.formData.password,callback:function(t){e.$set(e.formData,"password",t)},expression:"formData.password"}}),t("div",{staticStyle:{"margin-top":"3px",color:"#f56c6c","line-height":"1","font-size":"12px"}},[e._v(" 密码有效期为90天，请在期限前重置密码 ")])],1):e._e(),t("el-form-item",{staticClass:"block-label form-item-box",attrs:{label:"",prop:""}},[t("span",[e._v(" 到期修改密码 "),t("el-switch",{attrs:{disabled:!e.checkIsFormStatus,"active-color":"#ff9b45"},model:{value:e.formData.isExpireChangePwd,callback:function(t){e.$set(e.formData,"isExpireChangePwd",t)},expression:"formData.isExpireChangePwd"}}),e.formData.isExpireChangePwd?t("el-checkbox",{staticClass:"ps-checkbox",staticStyle:{"margin-left":"10px"},attrs:{disabled:!e.checkIsFormStatus},model:{value:e.formData.allowJumpChangePwd,callback:function(t){e.$set(e.formData,"allowJumpChangePwd",t)},expression:"formData.allowJumpChangePwd"}},[e._v(" 允许跳过本次 ")]):e._e()],1)]),t("div",{staticClass:"form-line"}),t("div",{staticClass:"l-title clearfix"},[t("span",{staticClass:"float-l min-title-h"},[e._v("联系方式")])]),t("el-row",{staticClass:"form-item-row-box",attrs:{gutter:24}},[t("el-col",{attrs:{span:12}},[t("el-form-item",{staticClass:"block-label",attrs:{label:"联系人：",prop:"contact"}},[e.checkIsFormStatus?t("el-input",{staticClass:"ps-input",attrs:{size:"small"},model:{value:e.formData.contact,callback:function(t){e.$set(e.formData,"contact",t)},expression:"formData.contact"}}):t("div",{staticClass:"item-form-text"},[e._v(e._s(e.formData.contact))])],1)],1),t("el-col",{attrs:{span:12}},[t("el-form-item",{staticClass:"block-label",attrs:{label:"手机号码：",prop:"mobile"}},[e.checkIsFormStatus?t("el-input",{staticClass:"ps-input",attrs:{size:"small"},model:{value:e.formData.mobile,callback:function(t){e.$set(e.formData,"mobile",t)},expression:"formData.mobile"}}):t("div",{staticClass:"item-form-text"},[e._v(e._s(e.formData.mobile))])],1)],1)],1),t("el-form-item",{staticClass:"block-label form-item-box",attrs:{label:"短信模板：",prop:"smsTemplateId"}},[e.checkIsFormStatus?t("el-input",{staticClass:"ps-input",attrs:{type:"textarea",rows:3},model:{value:e.formData.smsTemplateId,callback:function(t){e.$set(e.formData,"smsTemplateId",t)},expression:"formData.smsTemplateId"}}):t("div",{staticClass:"item-form-text"},[e._v(e._s(e.formData.smsTemplateId))])],1),t("el-form-item",{staticClass:"block-label form-item-box",attrs:{label:"备注：",prop:"remark"}},[e.checkIsFormStatus?t("el-input",{staticClass:"ps-input",attrs:{type:"textarea",rows:3},model:{value:e.formData.remark,callback:function(t){e.$set(e.formData,"remark",t)},expression:"formData.remark"}}):t("div",{staticClass:"item-form-text"},[e._v(e._s(e.formData.remark))])],1),t("div",{staticClass:"form-line"}),t("div",{staticClass:"l-title clearfix"},[t("span",{staticClass:"float-l min-title-h"},[e._v("其它设置")])]),t("el-form-item",{staticClass:"block-label form-item-box",attrs:{label:"钱包设置",prop:""}},[t("el-checkbox",{staticClass:"ps-checkbox",attrs:{disabled:!e.checkIsFormStatus},model:{value:e.formData.storeWalletOn,callback:function(t){e.$set(e.formData,"storeWalletOn",t)},expression:"formData.storeWalletOn"}},[e._v(" 储值钱包 ")]),t("el-checkbox",{staticClass:"ps-checkbox",attrs:{disabled:!e.checkIsFormStatus},model:{value:e.formData.electronicWalletOn,callback:function(t){e.$set(e.formData,"electronicWalletOn",t)},expression:"formData.electronicWalletOn"}},[e._v(" 电子钱包 ")]),t("el-checkbox",{staticClass:"ps-checkbox",attrs:{disabled:!e.checkIsFormStatus},model:{value:e.formData.subsidyWalletOn,callback:function(t){e.$set(e.formData,"subsidyWalletOn",t)},expression:"formData.subsidyWalletOn"}},[e._v(" 补贴钱包 ")]),t("el-checkbox",{staticClass:"ps-checkbox",attrs:{disabled:!e.checkIsFormStatus},model:{value:e.formData.complimentaryWalletOn,callback:function(t){e.$set(e.formData,"complimentaryWalletOn",t)},expression:"formData.complimentaryWalletOn"}},[e._v(" 赠送钱包 ")]),t("el-checkbox",{staticClass:"ps-checkbox",attrs:{disabled:!e.checkIsFormStatus},model:{value:e.formData.otherWalletOn,callback:function(t){e.$set(e.formData,"otherWalletOn",t)},expression:"formData.otherWalletOn"}},[e._v(" 第三方钱包 ")])],1),t("el-form-item",{staticClass:"form-item-box",attrs:{label:"开关设置",prop:""}},[t("span",{staticStyle:{"margin-right":"25px"}},[e._v(" 人脸支付 "),t("el-switch",{attrs:{disabled:!e.checkIsFormStatus,"active-color":"#ff9b45"},model:{value:e.formData.facepay,callback:function(t){e.$set(e.formData,"facepay",t)},expression:"formData.facepay"}})],1),t("span",[e._v(" 支持退款 "),t("el-switch",{attrs:{disabled:!e.checkIsFormStatus,"active-color":"#ff9b45"},model:{value:e.formData.refundOn,callback:function(t){e.$set(e.formData,"refundOn",t)},expression:"formData.refundOn"}})],1)]),t("el-form-item",{staticClass:"block-label form-item-box",attrs:{label:"",prop:""}},[t("span",[e._v(" 是否农行项目点展示 "),t("el-switch",{attrs:{disabled:!e.checkIsFormStatus,"active-color":"#ff9b45"},model:{value:e.formData.isAbcProject,callback:function(t){e.$set(e.formData,"isAbcProject",t)},expression:"formData.isAbcProject"}})],1)]),t("el-form-item",{staticClass:"block-label form-item-box",attrs:{label:"",prop:""}},[t("span",[e._v(" 开启会员功能 "),t("el-switch",{attrs:{disabled:!e.checkIsFormStatus,"active-color":"#ff9b45"},model:{value:e.formData.is_member_on,callback:function(t){e.$set(e.formData,"is_member_on",t)},expression:"formData.is_member_on"}})],1)]),t("el-form-item",{staticClass:"block-label form-item-box",attrs:{label:"",prop:""}},[t("span",[e._v("人脸更新消息提醒：")]),t("el-switch",{staticClass:"m-r-20",attrs:{disabled:!e.checkIsFormStatus,"active-color":"#ff9b45"},model:{value:e.formData.enableUpdateNotify,callback:function(t){e.$set(e.formData,"enableUpdateNotify",t)},expression:"formData.enableUpdateNotify"}}),e.formData.enableUpdateNotify?t("div",{staticStyle:{"margin-left":"125px"}},[t("span",{staticClass:"m-r-20"},[e._v("上传人脸时间每隔")]),t("el-form-item",{staticClass:"inline-label form-item-box m-t-2 m-b-2 m-r-20",attrs:{label:"",prop:"faceUpdateTime"}},[t("el-select",{staticClass:"w-110",attrs:{clearable:"",disabled:!e.checkIsFormStatus,placeholder:"请选择"},model:{value:e.formData.faceUpdateTime,callback:function(t){e.$set(e.formData,"faceUpdateTime",t)},expression:"formData.faceUpdateTime"}},e._l(e.faceUploadOptions,(function(e){return t("el-option",{key:e.value,attrs:{label:e.name,value:e.value}})})),1)],1),"auto"===e.formData.faceUpdateTime?t("el-form-item",{staticClass:"inline-label form-item-box m-t-2 m-b-2 m-r-20",attrs:{label:"",prop:"customFaceDate"}},[t("el-input",{staticClass:"w-100",attrs:{disabled:!e.checkIsFormStatus},model:{value:e.formData.customFaceDate,callback:function(t){e.$set(e.formData,"customFaceDate",t)},expression:"formData.customFaceDate"}}),t("span",{staticClass:"m-l-10"},[e._v("天")])],1):e._e(),t("span",{},[e._v("进行消息提醒")]),t("el-form-item",{staticClass:"block-label form-item-box",attrs:{label:"",prop:""}},[t("span",{staticStyle:{"vertical-align":"top"}},[e._v("提醒内容：")]),t("el-input",{staticStyle:{width:"70%"},attrs:{disabled:!e.checkIsFormStatus,type:"textarea",rows:2},model:{value:e.formData.notifyMsg,callback:function(t){e.$set(e.formData,"notifyMsg",t)},expression:"formData.notifyMsg"}})],1)],1):e._e()],1),"add"!==e.operate?t("div",[t("div",{staticClass:"form-line"}),t("div",{staticClass:"l-title clearfix"},[t("span",{staticClass:"float-l min-title-h"},[e._v(" 第三方设置 "),t("el-switch",{staticStyle:{"margin-left":"15px"},attrs:{disabled:!e.checkIsFormStatus,"active-color":"#ff9b45"},model:{value:e.formData.isThirdInterface,callback:function(t){e.$set(e.formData,"isThirdInterface",t)},expression:"formData.isThirdInterface"}})],1)]),t("div",{directives:[{name:"loading",rawName:"v-loading",value:e.loadingThirdInfo,expression:"loadingThirdInfo"},{name:"show",rawName:"v-show",value:e.formData.isThirdInterface,expression:"formData.isThirdInterface"}]},[t("el-row",{staticClass:"form-item-row-box",attrs:{gutter:24}},[t("el-col",{attrs:{span:12}},[t("el-form-item",{staticClass:"block-label",attrs:{label:"应用key：",prop:"thirdAppKey"}},[e.checkIsFormStatus?t("el-input",{staticClass:"ps-input",attrs:{size:"small"},model:{value:e.formData.thirdAppKey,callback:function(t){e.$set(e.formData,"thirdAppKey",t)},expression:"formData.thirdAppKey"}}):t("div",{staticClass:"item-form-text"},[e._v(e._s(e.formData.thirdAppKey))])],1)],1),t("el-col",{attrs:{span:12}},[t("el-form-item",{staticClass:"block-label",attrs:{label:"应用secret：",prop:"thirdSecretKey"}},[e.checkIsFormStatus?t("el-input",{staticClass:"ps-input",attrs:{size:"small"},model:{value:e.formData.thirdSecretKey,callback:function(t){e.$set(e.formData,"thirdSecretKey",t)},expression:"formData.thirdSecretKey"}}):t("el-tooltip",{staticClass:"item",attrs:{effect:"dark",content:e.formData.thirdSecretKey,placement:"top"}},[t("div",{staticClass:"item-form-text ellipsis"},[e._v(e._s(e.formData.thirdSecretKey))])])],1)],1)],1),t("el-form-item",{staticClass:"block-label",attrs:{label:"应用名称：",prop:"thirdAppName"}},[e.checkIsFormStatus?t("el-input",{staticClass:"ps-input",attrs:{size:"small"},model:{value:e.formData.thirdAppName,callback:function(t){e.$set(e.formData,"thirdAppName",t)},expression:"formData.thirdAppName"}}):t("div",{staticClass:"item-form-text ellipsis"},[e._v(e._s(e.formData.thirdAppName))])],1),t("el-row",{staticClass:"form-item-row-box",attrs:{gutter:24}},[t("el-col",{attrs:{span:12}},[t("el-form-item",{staticClass:"block-label",attrs:{label:"跳转地址：",prop:"thirdAppUrl"}},[e.checkIsFormStatus?t("el-input",{staticClass:"ps-input",attrs:{size:"small"},model:{value:e.formData.thirdAppUrl,callback:function(t){e.$set(e.formData,"thirdAppUrl",t)},expression:"formData.thirdAppUrl"}}):t("div",{staticClass:"item-form-text"},[e._v(e._s(e.formData.thirdAppUrl))])],1)],1),t("el-col",{attrs:{span:12}},[t("el-form-item",{staticClass:"block-label",attrs:{label:"回调地址：",prop:"thirdAppCallbackUrl"}},[e.checkIsFormStatus?t("el-input",{staticClass:"ps-input",attrs:{placeholder:"http://127.0.0.1/?userId={0}&bb=1，{0}将会被替换掉",size:"small"},model:{value:e.formData.thirdAppCallbackUrl,callback:function(t){e.$set(e.formData,"thirdAppCallbackUrl",t)},expression:"formData.thirdAppCallbackUrl"}}):t("div",{staticClass:"item-form-text"},[e._v(e._s(e.formData.thirdAppCallbackUrl))])],1)],1)],1),e.checkIsFormStatus?t("el-form-item",{staticClass:"block-center",attrs:{label:"",prop:""}},[t("el-button",{staticClass:"ps-origin-btn",attrs:{type:"primary"},on:{click:e.generateThirdAppinfo}},[e._v(" 重新生成 ")])],1):e._e()],1)]):e._e(),e.checkIsFormStatus?t("div",{staticClass:"form-footer"},[t("el-button",{attrs:{size:"small"},on:{click:e.cancelFormHandle}},[e._v("取消")]),t("el-button",{directives:[{name:"permission",rawName:"v-permission",value:["background.admin.organization.add_root","background.admin.organization.modify"],expression:"[\n          'background.admin.organization.add_root',\n          'background.admin.organization.modify'\n        ]"}],staticClass:"ps-origin-btn",attrs:{type:"primary",size:"small"},on:{click:e.sendFormdataHandle}},[e._v(" 保存 ")])],1):e._e()],1)],1)}),[],!1,null,null,null);t.default=b.exports}}]);